/** @license zlib.js 2012 - imaya [ https://github.com/imaya/zlib.js ] The MIT License */(function() {'use strict';function l(a){throw a;}var r=void 0,t,aa=this;function v(a,b){var c=a.split("."),d=aa;!(c[0]in d)&&d.execScript&&d.execScript("var "+c[0]);for(var f;c.length&&(f=c.shift());)!c.length&&b!==r?d[f]=b:d=d[f]?d[f]:d[f]={}};var y="undefined"!==typeof Uint8Array&&"undefined"!==typeof Uint16Array&&"undefined"!==typeof Uint32Array&&"undefined"!==typeof DataView;new (y?Uint8Array:Array)(256);var z;for(z=0;256>z;++z)for(var B=z,ba=7,B=B>>>1;B;B>>>=1)--ba;var ca=[0,**********,**********,**********,124634137,**********,**********,**********,249268274,**********,**********,**********,162941995,**********,**********,**********,498536548,**********,**********,**********,450548861,**********,**********,**********,325883990,**********,**********,**********,335633487,**********,**********,**********,997073096,**********,**********,**********,**********,**********,**********,**********,901097722,**********,**********,**********,853044451,**********,**********,
2882616665,651767980,1373503546,3369554304,3218104598,565507253,1454621731,3485111705,3099436303,671266974,1594198024,3322730930,2970347812,795835527,1483230225,3244367275,3060149565,1994146192,31158534,2563907772,4023717930,1907459465,112637215,2680153253,3904427059,2013776290,251722036,2517215374,3775830040,2137656763,141376813,2439277719,3865271297,1802195444,476864866,2238001368,4066508878,1812370925,453092731,2181625025,4111451223,1706088902,314042704,2344532202,4240017532,1658658271,366619977,
2362670323,4224994405,1303535960,984961486,2747007092,3569037538,1256170817,1037604311,2765210733,3554079995,1131014506,879679996,2909243462,3663771856,1141124467,855842277,2852801631,3708648649,1342533948,654459306,3188396048,3373015174,1466479909,544179635,3110523913,3462522015,1591671054,702138776,2966460450,3352799412,1504918807,783551873,3082640443,3233442989,3988292384,2596254646,62317068,1957810842,3939845945,2647816111,81470997,1943803523,3814918930,2489596804,225274430,2053790376,3826175755,
2466906013,167816743,2097651377,4027552580,2265490386,503444072,1762050814,4150417245,2154129355,426522225,1852507879,4275313526,2312317920,282753626,1742555852,4189708143,2394877945,397917763,1622183637,3604390888,2714866558,953729732,1340076626,3518719985,2797360999,1068828381,1219638859,3624741850,2936675148,906185462,1090812512,3747672003,2825379669,829329135,1181335161,3412177804,3160834842,628085408,1382605366,3423369109,3138078467,570562233,1426400815,3317316542,2998733608,733239954,1555261956,
3268935591,3050360625,752459403,1541320221,2607071920,3965973030,1969922972,40735498,2617837225,3943577151,1913087877,83908371,2512341634,3803740692,2075208622,213261112,2463272603,3855990285,2094854071,198958881,2262029012,4057260610,1759359992,534414190,2176718541,4139329115,1873836001,414664567,2282248934,4279200368,1711684554,285281116,2405801727,4167216745,1634467795,376229701,2685067896,3608007406,1308918612,956543938,2808555105,3495958263,1231636301,1047427035,2932959818,3654703836,1088359270,
936918E3,2847714899,3736837829,1202900863,817233897,3183342108,3401237130,1404277552,615818150,3134207493,3453421203,1423857449,601450431,3009837614,3294710456,1567103746,711928724,3020668471,3272380065,1510334235,755167117],C=y?new Uint32Array(ca):ca;if(aa.Uint8Array!==r)try{eval("String.fromCharCode.apply(null, new Uint8Array([0]));")}catch(ea){String.fromCharCode.apply=function(a){return function(b,c){return a.call(String.fromCharCode,b,Array.prototype.slice.call(c))}}(String.fromCharCode.apply)};function D(a){var b=a.length,c=0,d=Number.POSITIVE_INFINITY,f,h,k,e,g,m,p,s,q,x;for(s=0;s<b;++s)a[s]>c&&(c=a[s]),a[s]<d&&(d=a[s]);f=1<<c;h=new (y?Uint32Array:Array)(f);k=1;e=0;for(g=2;k<=c;){for(s=0;s<b;++s)if(a[s]===k){m=0;p=e;for(q=0;q<k;++q)m=m<<1|p&1,p>>=1;x=k<<16|s;for(q=m;q<f;q+=g)h[q]=x;++e}++k;e<<=1;g<<=1}return[h,c,d]};var F=[],G;for(G=0;288>G;G++)switch(!0){case 143>=G:F.push([G+48,8]);break;case 255>=G:F.push([G-144+400,9]);break;case 279>=G:F.push([G-256+0,7]);break;case 287>=G:F.push([G-280+192,8]);break;default:l("invalid literal: "+G)}
var fa=function(){function a(a){switch(!0){case 3===a:return[257,a-3,0];case 4===a:return[258,a-4,0];case 5===a:return[259,a-5,0];case 6===a:return[260,a-6,0];case 7===a:return[261,a-7,0];case 8===a:return[262,a-8,0];case 9===a:return[263,a-9,0];case 10===a:return[264,a-10,0];case 12>=a:return[265,a-11,1];case 14>=a:return[266,a-13,1];case 16>=a:return[267,a-15,1];case 18>=a:return[268,a-17,1];case 22>=a:return[269,a-19,2];case 26>=a:return[270,a-23,2];case 30>=a:return[271,a-27,2];case 34>=a:return[272,
a-31,2];case 42>=a:return[273,a-35,3];case 50>=a:return[274,a-43,3];case 58>=a:return[275,a-51,3];case 66>=a:return[276,a-59,3];case 82>=a:return[277,a-67,4];case 98>=a:return[278,a-83,4];case 114>=a:return[279,a-99,4];case 130>=a:return[280,a-115,4];case 162>=a:return[281,a-131,5];case 194>=a:return[282,a-163,5];case 226>=a:return[283,a-195,5];case 257>=a:return[284,a-227,5];case 258===a:return[285,a-258,0];default:l("invalid length: "+a)}}var b=[],c,d;for(c=3;258>=c;c++)d=a(c),b[c]=d[2]<<24|d[1]<<
16|d[0];return b}();y&&new Uint32Array(fa);function I(a,b){this.l=[];this.m=32768;this.d=this.f=this.c=this.t=0;this.input=y?new Uint8Array(a):a;this.u=!1;this.n=J;this.K=!1;if(b||!(b={}))b.index&&(this.c=b.index),b.bufferSize&&(this.m=b.bufferSize),b.bufferType&&(this.n=b.bufferType),b.resize&&(this.K=b.resize);switch(this.n){case ga:this.a=32768;this.b=new (y?Uint8Array:Array)(32768+this.m+258);break;case J:this.a=0;this.b=new (y?Uint8Array:Array)(this.m);this.e=this.W;this.B=this.R;this.q=this.V;break;default:l(Error("invalid inflate mode"))}}
var ga=0,J=1;
I.prototype.r=function(){for(;!this.u;){var a=K(this,3);a&1&&(this.u=!0);a>>>=1;switch(a){case 0:var b=this.input,c=this.c,d=this.b,f=this.a,h=b.length,k=r,e=r,g=d.length,m=r;this.d=this.f=0;c+1>=h&&l(Error("invalid uncompressed block header: LEN"));k=b[c++]|b[c++]<<8;c+1>=h&&l(Error("invalid uncompressed block header: NLEN"));e=b[c++]|b[c++]<<8;k===~e&&l(Error("invalid uncompressed block header: length verify"));c+k>b.length&&l(Error("input buffer is broken"));switch(this.n){case ga:for(;f+k>d.length;){m=
g-f;k-=m;if(y)d.set(b.subarray(c,c+m),f),f+=m,c+=m;else for(;m--;)d[f++]=b[c++];this.a=f;d=this.e();f=this.a}break;case J:for(;f+k>d.length;)d=this.e({H:2});break;default:l(Error("invalid inflate mode"))}if(y)d.set(b.subarray(c,c+k),f),f+=k,c+=k;else for(;k--;)d[f++]=b[c++];this.c=c;this.a=f;this.b=d;break;case 1:this.q(ha,ia);break;case 2:for(var p=K(this,5)+257,s=K(this,5)+1,q=K(this,4)+4,x=new (y?Uint8Array:Array)(L.length),u=r,n=r,E=r,A=r,X=r,O=r,H=r,w=r,da=r,w=0;w<q;++w)x[L[w]]=K(this,3);if(!y){w=
q;for(q=x.length;w<q;++w)x[L[w]]=0}u=D(x);A=new (y?Uint8Array:Array)(p+s);w=0;for(da=p+s;w<da;)switch(X=M(this,u),X){case 16:for(H=3+K(this,2);H--;)A[w++]=O;break;case 17:for(H=3+K(this,3);H--;)A[w++]=0;O=0;break;case 18:for(H=11+K(this,7);H--;)A[w++]=0;O=0;break;default:O=A[w++]=X}n=y?D(A.subarray(0,p)):D(A.slice(0,p));E=y?D(A.subarray(p)):D(A.slice(p));this.q(n,E);break;default:l(Error("unknown BTYPE: "+a))}}return this.B()};
var ja=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],L=y?new Uint16Array(ja):ja,ka=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,258,258],la=y?new Uint16Array(ka):ka,ma=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0],N=y?new Uint8Array(ma):ma,na=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577],oa=y?new Uint16Array(na):na,pa=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,
11,11,12,12,13,13],P=y?new Uint8Array(pa):pa,Q=new (y?Uint8Array:Array)(288),R,qa;R=0;for(qa=Q.length;R<qa;++R)Q[R]=143>=R?8:255>=R?9:279>=R?7:8;var ha=D(Q),S=new (y?Uint8Array:Array)(30),T,ra;T=0;for(ra=S.length;T<ra;++T)S[T]=5;var ia=D(S);function K(a,b){for(var c=a.f,d=a.d,f=a.input,h=a.c,k=f.length,e;d<b;)h>=k&&l(Error("input buffer is broken")),c|=f[h++]<<d,d+=8;e=c&(1<<b)-1;a.f=c>>>b;a.d=d-b;a.c=h;return e}
function M(a,b){for(var c=a.f,d=a.d,f=a.input,h=a.c,k=f.length,e=b[0],g=b[1],m,p;d<g&&!(h>=k);)c|=f[h++]<<d,d+=8;m=e[c&(1<<g)-1];p=m>>>16;p>d&&l(Error("invalid code length: "+p));a.f=c>>p;a.d=d-p;a.c=h;return m&65535}t=I.prototype;
t.q=function(a,b){var c=this.b,d=this.a;this.C=a;for(var f=c.length-258,h,k,e,g;256!==(h=M(this,a));)if(256>h)d>=f&&(this.a=d,c=this.e(),d=this.a),c[d++]=h;else{k=h-257;g=la[k];0<N[k]&&(g+=K(this,N[k]));h=M(this,b);e=oa[h];0<P[h]&&(e+=K(this,P[h]));d>=f&&(this.a=d,c=this.e(),d=this.a);for(;g--;)c[d]=c[d++-e]}for(;8<=this.d;)this.d-=8,this.c--;this.a=d};
t.V=function(a,b){var c=this.b,d=this.a;this.C=a;for(var f=c.length,h,k,e,g;256!==(h=M(this,a));)if(256>h)d>=f&&(c=this.e(),f=c.length),c[d++]=h;else{k=h-257;g=la[k];0<N[k]&&(g+=K(this,N[k]));h=M(this,b);e=oa[h];0<P[h]&&(e+=K(this,P[h]));d+g>f&&(c=this.e(),f=c.length);for(;g--;)c[d]=c[d++-e]}for(;8<=this.d;)this.d-=8,this.c--;this.a=d};
t.e=function(){var a=new (y?Uint8Array:Array)(this.a-32768),b=this.a-32768,c,d,f=this.b;if(y)a.set(f.subarray(32768,a.length));else{c=0;for(d=a.length;c<d;++c)a[c]=f[c+32768]}this.l.push(a);this.t+=a.length;if(y)f.set(f.subarray(b,b+32768));else for(c=0;32768>c;++c)f[c]=f[b+c];this.a=32768;return f};
t.W=function(a){var b,c=this.input.length/this.c+1|0,d,f,h,k=this.input,e=this.b;a&&("number"===typeof a.H&&(c=a.H),"number"===typeof a.P&&(c+=a.P));2>c?(d=(k.length-this.c)/this.C[2],h=258*(d/2)|0,f=h<e.length?e.length+h:e.length<<1):f=e.length*c;y?(b=new Uint8Array(f),b.set(e)):b=e;return this.b=b};
t.B=function(){var a=0,b=this.b,c=this.l,d,f=new (y?Uint8Array:Array)(this.t+(this.a-32768)),h,k,e,g;if(0===c.length)return y?this.b.subarray(32768,this.a):this.b.slice(32768,this.a);h=0;for(k=c.length;h<k;++h){d=c[h];e=0;for(g=d.length;e<g;++e)f[a++]=d[e]}h=32768;for(k=this.a;h<k;++h)f[a++]=b[h];this.l=[];return this.buffer=f};
t.R=function(){var a,b=this.a;y?this.K?(a=new Uint8Array(b),a.set(this.b.subarray(0,b))):a=this.b.subarray(0,b):(this.b.length>b&&(this.b.length=b),a=this.b);return this.buffer=a};function U(a){a=a||{};this.files=[];this.v=a.comment}U.prototype.L=function(a){this.j=a};U.prototype.s=function(a){var b=a[2]&65535|2;return b*(b^1)>>8&255};U.prototype.k=function(a,b){a[0]=(C[(a[0]^b)&255]^a[0]>>>8)>>>0;a[1]=(6681*(20173*(a[1]+(a[0]&255))>>>0)>>>0)+1>>>0;a[2]=(C[(a[2]^a[1]>>>24)&255]^a[2]>>>8)>>>0};U.prototype.T=function(a){var b=[305419896,591751049,878082192],c,d;y&&(b=new Uint32Array(b));c=0;for(d=a.length;c<d;++c)this.k(b,a[c]&255);return b};function V(a,b){b=b||{};this.input=y&&a instanceof Array?new Uint8Array(a):a;this.c=0;this.ba=b.verify||!1;this.j=b.password}var sa={O:0,M:8},W=[80,75,1,2],Y=[80,75,3,4],Z=[80,75,5,6];function ta(a,b){this.input=a;this.offset=b}
ta.prototype.parse=function(){var a=this.input,b=this.offset;(a[b++]!==W[0]||a[b++]!==W[1]||a[b++]!==W[2]||a[b++]!==W[3])&&l(Error("invalid file header signature"));this.version=a[b++];this.ia=a[b++];this.Z=a[b++]|a[b++]<<8;this.I=a[b++]|a[b++]<<8;this.A=a[b++]|a[b++]<<8;this.time=a[b++]|a[b++]<<8;this.U=a[b++]|a[b++]<<8;this.p=(a[b++]|a[b++]<<8|a[b++]<<16|a[b++]<<24)>>>0;this.z=(a[b++]|a[b++]<<8|a[b++]<<16|a[b++]<<24)>>>0;this.J=(a[b++]|a[b++]<<8|a[b++]<<16|a[b++]<<24)>>>0;this.h=a[b++]|a[b++]<<
8;this.g=a[b++]|a[b++]<<8;this.F=a[b++]|a[b++]<<8;this.ea=a[b++]|a[b++]<<8;this.ga=a[b++]|a[b++]<<8;this.fa=a[b++]|a[b++]<<8|a[b++]<<16|a[b++]<<24;this.$=(a[b++]|a[b++]<<8|a[b++]<<16|a[b++]<<24)>>>0;this.filename=String.fromCharCode.apply(null,y?a.subarray(b,b+=this.h):a.slice(b,b+=this.h));this.X=y?a.subarray(b,b+=this.g):a.slice(b,b+=this.g);this.v=y?a.subarray(b,b+this.F):a.slice(b,b+this.F);this.length=b-this.offset};function ua(a,b){this.input=a;this.offset=b}var va={N:1,ca:8,da:2048};
ua.prototype.parse=function(){var a=this.input,b=this.offset;(a[b++]!==Y[0]||a[b++]!==Y[1]||a[b++]!==Y[2]||a[b++]!==Y[3])&&l(Error("invalid local file header signature"));this.Z=a[b++]|a[b++]<<8;this.I=a[b++]|a[b++]<<8;this.A=a[b++]|a[b++]<<8;this.time=a[b++]|a[b++]<<8;this.U=a[b++]|a[b++]<<8;this.p=(a[b++]|a[b++]<<8|a[b++]<<16|a[b++]<<24)>>>0;this.z=(a[b++]|a[b++]<<8|a[b++]<<16|a[b++]<<24)>>>0;this.J=(a[b++]|a[b++]<<8|a[b++]<<16|a[b++]<<24)>>>0;this.h=a[b++]|a[b++]<<8;this.g=a[b++]|a[b++]<<8;this.filename=
String.fromCharCode.apply(null,y?a.subarray(b,b+=this.h):a.slice(b,b+=this.h));this.X=y?a.subarray(b,b+=this.g):a.slice(b,b+=this.g);this.length=b-this.offset};
function $(a){var b=[],c={},d,f,h,k;if(!a.i){if(a.o===r){var e=a.input,g;if(!a.D)a:{var m=a.input,p;for(p=m.length-12;0<p;--p)if(m[p]===Z[0]&&m[p+1]===Z[1]&&m[p+2]===Z[2]&&m[p+3]===Z[3]){a.D=p;break a}l(Error("End of Central Directory Record not found"))}g=a.D;(e[g++]!==Z[0]||e[g++]!==Z[1]||e[g++]!==Z[2]||e[g++]!==Z[3])&&l(Error("invalid signature"));a.ha=e[g++]|e[g++]<<8;a.ja=e[g++]|e[g++]<<8;a.ka=e[g++]|e[g++]<<8;a.aa=e[g++]|e[g++]<<8;a.Q=(e[g++]|e[g++]<<8|e[g++]<<16|e[g++]<<24)>>>0;a.o=(e[g++]|
e[g++]<<8|e[g++]<<16|e[g++]<<24)>>>0;a.w=e[g++]|e[g++]<<8;a.v=y?e.subarray(g,g+a.w):e.slice(g,g+a.w)}d=a.o;h=0;for(k=a.aa;h<k;++h)f=new ta(a.input,d),f.parse(),d+=f.length,b[h]=f,c[f.filename]=h;a.Q<d-a.o&&l(Error("invalid file header size"));a.i=b;a.G=c}}t=V.prototype;t.Y=function(){var a=[],b,c,d;this.i||$(this);d=this.i;b=0;for(c=d.length;b<c;++b)a[b]=d[b].filename;return a};
t.r=function(a,b){var c;this.G||$(this);c=this.G[a];c===r&&l(Error(a+" not found"));var d;d=b||{};var f=this.input,h=this.i,k,e,g,m,p,s,q,x;h||$(this);h[c]===r&&l(Error("wrong index"));e=h[c].$;k=new ua(this.input,e);k.parse();e+=k.length;g=k.z;if(0!==(k.I&va.N)){!d.password&&!this.j&&l(Error("please set password"));s=this.S(d.password||this.j);q=e;for(x=e+12;q<x;++q)wa(this,s,f[q]);e+=12;g-=12;q=e;for(x=e+g;q<x;++q)f[q]=wa(this,s,f[q])}switch(k.A){case sa.O:m=y?this.input.subarray(e,e+g):this.input.slice(e,
e+g);break;case sa.M:m=(new I(this.input,{index:e,bufferSize:k.J})).r();break;default:l(Error("unknown compression type"))}if(this.ba){var u=r,n,E="number"===typeof u?u:u=0,A=m.length;n=-1;for(E=A&7;E--;++u)n=n>>>8^C[(n^m[u])&255];for(E=A>>3;E--;u+=8)n=n>>>8^C[(n^m[u])&255],n=n>>>8^C[(n^m[u+1])&255],n=n>>>8^C[(n^m[u+2])&255],n=n>>>8^C[(n^m[u+3])&255],n=n>>>8^C[(n^m[u+4])&255],n=n>>>8^C[(n^m[u+5])&255],n=n>>>8^C[(n^m[u+6])&255],n=n>>>8^C[(n^m[u+7])&255];p=(n^4294967295)>>>0;k.p!==p&&l(Error("wrong crc: file=0x"+
k.p.toString(16)+", data=0x"+p.toString(16)))}return m};t.L=function(a){this.j=a};function wa(a,b,c){c^=a.s(b);a.k(b,c);return c}t.k=U.prototype.k;t.S=U.prototype.T;t.s=U.prototype.s;v("Zlib.Unzip",V);v("Zlib.Unzip.prototype.decompress",V.prototype.r);v("Zlib.Unzip.prototype.getFilenames",V.prototype.Y);v("Zlib.Unzip.prototype.setPassword",V.prototype.L);}).call(this);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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