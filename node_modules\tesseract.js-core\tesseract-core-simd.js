
var TesseractCore = (() => {
  var _scriptDir = typeof document !== 'undefined' && document.currentScript ? document.currentScript.src : undefined;
  if (typeof __filename !== 'undefined') _scriptDir = _scriptDir || __filename;
  return (
function(TesseractCore = {})  {

var b;b||(b=typeof TesseractCore !== 'undefined' ? TesseractCore : {});var aa,ba;b.ready=new Promise((a,c)=>{aa=a;ba=c});var ca=Object.assign({},b),da="./this.program",ea=(a,c)=>{throw c;},fa="object"==typeof window,ha="function"==typeof importScripts,ia="object"==typeof process&&"object"==typeof process.versions&&"string"==typeof process.versions.node,f="",ja,ka,la;
if(ia){var fs=require("fs"),ma=require("path");f=ha?ma.dirname(f)+"/":__dirname+"/";ja=(a,c)=>{a=a.startsWith("file://")?new URL(a):ma.normalize(a);return fs.readFileSync(a,c?void 0:"utf8")};la=a=>{a=ja(a,!0);a.buffer||(a=new Uint8Array(a));return a};ka=(a,c,d,e=!0)=>{a=a.startsWith("file://")?new URL(a):ma.normalize(a);fs.readFile(a,e?void 0:"utf8",(g,h)=>{g?d(g):c(e?h.buffer:h)})};!b.thisProgram&&1<process.argv.length&&(da=process.argv[1].replace(/\\/g,"/"));process.argv.slice(2);ea=(a,c)=>{process.exitCode=
a;throw c;};b.inspect=()=>"[Emscripten Module object]"}else if(fa||ha)ha?f=self.location.href:"undefined"!=typeof document&&document.currentScript&&(f=document.currentScript.src),_scriptDir&&(f=_scriptDir),0!==f.indexOf("blob:")?f=f.substr(0,f.replace(/[?#].*/,"").lastIndexOf("/")+1):f="",ja=a=>{var c=new XMLHttpRequest;c.open("GET",a,!1);c.send(null);return c.responseText},ha&&(la=a=>{var c=new XMLHttpRequest;c.open("GET",a,!1);c.responseType="arraybuffer";c.send(null);return new Uint8Array(c.response)}),
ka=(a,c,d)=>{var e=new XMLHttpRequest;e.open("GET",a,!0);e.responseType="arraybuffer";e.onload=()=>{200==e.status||0==e.status&&e.response?c(e.response):d()};e.onerror=d;e.send(null)};var na=b.print||console.log.bind(console),oa=b.printErr||console.warn.bind(console);Object.assign(b,ca);ca=null;b.thisProgram&&(da=b.thisProgram);b.quit&&(ea=b.quit);var pa;b.wasmBinary&&(pa=b.wasmBinary);var noExitRuntime=b.noExitRuntime||!0;"object"!=typeof WebAssembly&&n("no native wasm support detected");
var ra,sa=!1,p,ta,ua,r,u,va,wa;function xa(){var a=ra.buffer;b.HEAP8=p=new Int8Array(a);b.HEAP16=ua=new Int16Array(a);b.HEAP32=r=new Int32Array(a);b.HEAPU8=ta=new Uint8Array(a);b.HEAPU16=new Uint16Array(a);b.HEAPU32=u=new Uint32Array(a);b.HEAPF32=va=new Float32Array(a);b.HEAPF64=wa=new Float64Array(a)}var ya,za=[],Aa=[],Ba=[],Ca=!1;function Da(){var a=b.preRun.shift();za.unshift(a)}var Ea=0,Fa=null,Ga=null;function Ha(){Ea++;b.monitorRunDependencies&&b.monitorRunDependencies(Ea)}
function Ia(){Ea--;b.monitorRunDependencies&&b.monitorRunDependencies(Ea);if(0==Ea&&(null!==Fa&&(clearInterval(Fa),Fa=null),Ga)){var a=Ga;Ga=null;a()}}function n(a){if(b.onAbort)b.onAbort(a);a="Aborted("+a+")";oa(a);sa=!0;a=new WebAssembly.RuntimeError(a+". Build with -sASSERTIONS for more info.");ba(a);throw a;}function Ja(a){return a.startsWith("data:application/octet-stream;base64,")}var Ka;Ka="tesseract-core-simd.wasm";if(!Ja(Ka)){var La=Ka;Ka=b.locateFile?b.locateFile(La,f):f+La}
function Ma(a){try{if(a==Ka&&pa)return new Uint8Array(pa);if(la)return la(a);throw"both async and sync fetching of the wasm failed";}catch(c){n(c)}}function Na(a){if(!pa&&(fa||ha)){if("function"==typeof fetch&&!a.startsWith("file://"))return fetch(a,{credentials:"same-origin"}).then(c=>{if(!c.ok)throw"failed to load wasm binary file at '"+a+"'";return c.arrayBuffer()}).catch(()=>Ma(a));if(ka)return new Promise((c,d)=>{ka(a,e=>c(new Uint8Array(e)),d)})}return Promise.resolve().then(()=>Ma(a))}
function Oa(a,c,d){return Na(a).then(e=>WebAssembly.instantiate(e,c)).then(e=>e).then(d,e=>{oa("failed to asynchronously prepare wasm: "+e);n(e)})}
function Pa(a,c){var d=Ka;return pa||"function"!=typeof WebAssembly.instantiateStreaming||Ja(d)||d.startsWith("file://")||ia||"function"!=typeof fetch?Oa(d,a,c):fetch(d,{credentials:"same-origin"}).then(e=>WebAssembly.instantiateStreaming(e,a).then(c,function(g){oa("wasm streaming compile failed: "+g);oa("falling back to ArrayBuffer instantiation");return Oa(d,a,c)}))}
var x,y,Qa={628252:a=>{b.TesseractProgress&&b.TesseractProgress(a)},628321:a=>{b.TesseractProgress&&b.TesseractProgress(a)},628390:a=>{b.TesseractProgress&&b.TesseractProgress(a)}};function Ra(a){this.name="ExitStatus";this.message="Program terminated with exit("+a+")";this.status=a}function Sa(a){for(;0<a.length;)a.shift()(b)}function Ta(a){for(var c=0,d=0;d<a.length;++d){var e=a.charCodeAt(d);127>=e?c++:2047>=e?c+=2:55296<=e&&57343>=e?(c+=4,++d):c+=3}return c}
function Ua(a,c,d,e){if(!(0<e))return 0;var g=d;e=d+e-1;for(var h=0;h<a.length;++h){var k=a.charCodeAt(h);if(55296<=k&&57343>=k){var m=a.charCodeAt(++h);k=65536+((k&1023)<<10)|m&1023}if(127>=k){if(d>=e)break;c[d++]=k}else{if(2047>=k){if(d+1>=e)break;c[d++]=192|k>>6}else{if(65535>=k){if(d+2>=e)break;c[d++]=224|k>>12}else{if(d+3>=e)break;c[d++]=240|k>>18;c[d++]=128|k>>12&63}c[d++]=128|k>>6&63}c[d++]=128|k&63}}c[d]=0;return d-g}var Va="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0;
function Wa(a,c){for(var d=c+NaN,e=c;a[e]&&!(e>=d);)++e;if(16<e-c&&a.buffer&&Va)return Va.decode(a.subarray(c,e));for(d="";c<e;){var g=a[c++];if(g&128){var h=a[c++]&63;if(192==(g&224))d+=String.fromCharCode((g&31)<<6|h);else{var k=a[c++]&63;g=224==(g&240)?(g&15)<<12|h<<6|k:(g&7)<<18|h<<12|k<<6|a[c++]&63;65536>g?d+=String.fromCharCode(g):(g-=65536,d+=String.fromCharCode(55296|g>>10,56320|g&1023))}}else d+=String.fromCharCode(g)}return d}function z(a){return a?Wa(ta,a):""}
function Xa(a,c="i8"){c.endsWith("*")&&(c="*");switch(c){case "i1":return p[a>>0];case "i8":return p[a>>0];case "i16":return ua[a>>1];case "i32":return r[a>>2];case "i64":return r[a>>2];case "float":return va[a>>2];case "double":return wa[a>>3];case "*":return u[a>>2];default:n("invalid type for getValue: "+c)}}
function Ya(a,c,d="i8"){d.endsWith("*")&&(d="*");switch(d){case "i1":p[a>>0]=c;break;case "i8":p[a>>0]=c;break;case "i16":ua[a>>1]=c;break;case "i32":r[a>>2]=c;break;case "i64":y=[c>>>0,(x=c,1<=+Math.abs(x)?0<x?+Math.floor(x/4294967296)>>>0:~~+Math.ceil((x-+(~~x>>>0))/4294967296)>>>0:0)];r[a>>2]=y[0];r[a+4>>2]=y[1];break;case "float":va[a>>2]=c;break;case "double":wa[a>>3]=c;break;case "*":u[a>>2]=c;break;default:n("invalid type for setValue: "+d)}}
function Za(a){this.Df=a-24;this.th=function(c){u[this.Df+4>>2]=c};this.Dg=function(c){u[this.Df+8>>2]=c};this.dg=function(c,d){this.Qf();this.th(c);this.Dg(d)};this.Qf=function(){u[this.Df+16>>2]=0}}
var $a=0,ab=0,bb=(a,c)=>{for(var d=0,e=a.length-1;0<=e;e--){var g=a[e];"."===g?a.splice(e,1):".."===g?(a.splice(e,1),d++):d&&(a.splice(e,1),d--)}if(c)for(;d;d--)a.unshift("..");return a},cb=a=>{var c="/"===a.charAt(0),d="/"===a.substr(-1);(a=bb(a.split("/").filter(e=>!!e),!c).join("/"))||c||(a=".");a&&d&&(a+="/");return(c?"/":"")+a},db=a=>{var c=/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/.exec(a).slice(1);a=c[0];c=c[1];if(!a&&!c)return".";c&&(c=c.substr(0,c.length-1));return a+
c},eb=a=>{if("/"===a)return"/";a=cb(a);a=a.replace(/\/$/,"");var c=a.lastIndexOf("/");return-1===c?a:a.substr(c+1)},fb=(a,c)=>cb(a+"/"+c);function gb(){if("object"==typeof crypto&&"function"==typeof crypto.getRandomValues)return d=>crypto.getRandomValues(d);if(ia)try{var a=require("crypto");if(a.randomFillSync)return d=>a.randomFillSync(d);var c=a.randomBytes;return d=>(d.set(c(d.byteLength)),d)}catch(d){}n("initRandomDevice")}function hb(a){return(hb=gb())(a)}
function ib(){for(var a="",c=!1,d=arguments.length-1;-1<=d&&!c;d--){c=0<=d?arguments[d]:A.cwd();if("string"!=typeof c)throw new TypeError("Arguments to path.resolve must be strings");if(!c)return"";a=c+"/"+a;c="/"===c.charAt(0)}a=bb(a.split("/").filter(e=>!!e),!c).join("/");return(c?"/":"")+a||"."}
var jb=(a,c)=>{function d(k){for(var m=0;m<k.length&&""===k[m];m++);for(var v=k.length-1;0<=v&&""===k[v];v--);return m>v?[]:k.slice(m,v-m+1)}a=ib(a).substr(1);c=ib(c).substr(1);a=d(a.split("/"));c=d(c.split("/"));for(var e=Math.min(a.length,c.length),g=e,h=0;h<e;h++)if(a[h]!==c[h]){g=h;break}e=[];for(h=g;h<a.length;h++)e.push("..");e=e.concat(c.slice(g));return e.join("/")};function kb(a,c){var d=Array(Ta(a)+1);a=Ua(a,d,0,d.length);c&&(d.length=a);return d}var lb=[];
function mb(a,c){lb[a]={input:[],output:[],ng:c};A.Yg(a,nb)}
var nb={open:function(a){var c=lb[a.node.rdev];if(!c)throw new A.Ef(43);a.tty=c;a.seekable=!1},close:function(a){a.tty.ng.fsync(a.tty)},fsync:function(a){a.tty.ng.fsync(a.tty)},read:function(a,c,d,e){if(!a.tty||!a.tty.ng.lh)throw new A.Ef(60);for(var g=0,h=0;h<e;h++){try{var k=a.tty.ng.lh(a.tty)}catch(m){throw new A.Ef(29);}if(void 0===k&&0===g)throw new A.Ef(6);if(null===k||void 0===k)break;g++;c[d+h]=k}g&&(a.node.timestamp=Date.now());return g},write:function(a,c,d,e){if(!a.tty||!a.tty.ng.Vg)throw new A.Ef(60);
try{for(var g=0;g<e;g++)a.tty.ng.Vg(a.tty,c[d+g])}catch(h){throw new A.Ef(29);}e&&(a.node.timestamp=Date.now());return g}},ob={lh:function(a){if(!a.input.length){var c=null;if(ia){var d=Buffer.alloc(256),e=0;try{e=fs.readSync(process.stdin.fd,d,0,256,-1)}catch(g){if(g.toString().includes("EOF"))e=0;else throw g;}0<e?c=d.slice(0,e).toString("utf-8"):c=null}else"undefined"!=typeof window&&"function"==typeof window.prompt?(c=window.prompt("Input: "),null!==c&&(c+="\n")):"function"==typeof readline&&
(c=readline(),null!==c&&(c+="\n"));if(!c)return null;a.input=kb(c,!0)}return a.input.shift()},Vg:function(a,c){null===c||10===c?(na(Wa(a.output,0)),a.output=[]):0!=c&&a.output.push(c)},fsync:function(a){a.output&&0<a.output.length&&(na(Wa(a.output,0)),a.output=[])}},pb={Vg:function(a,c){null===c||10===c?(oa(Wa(a.output,0)),a.output=[]):0!=c&&a.output.push(c)},fsync:function(a){a.output&&0<a.output.length&&(oa(Wa(a.output,0)),a.output=[])}},B={Wf:null,Nf:function(){return B.createNode(null,"/",16895,
0)},createNode:function(a,c,d,e){if(A.fi(d)||A.isFIFO(d))throw new A.Ef(63);B.Wf||(B.Wf={dir:{node:{Tf:B.Ff.Tf,Pf:B.Ff.Pf,lookup:B.Ff.lookup,$f:B.Ff.$f,rename:B.Ff.rename,unlink:B.Ff.unlink,rmdir:B.Ff.rmdir,readdir:B.Ff.readdir,symlink:B.Ff.symlink},stream:{Uf:B.Hf.Uf}},file:{node:{Tf:B.Ff.Tf,Pf:B.Ff.Pf},stream:{Uf:B.Hf.Uf,read:B.Hf.read,write:B.Hf.write,og:B.Hf.og,gg:B.Hf.gg,mg:B.Hf.mg}},link:{node:{Tf:B.Ff.Tf,Pf:B.Ff.Pf,readlink:B.Ff.readlink},stream:{}},ah:{node:{Tf:B.Ff.Tf,Pf:B.Ff.Pf},stream:A.Bh}});
d=A.createNode(a,c,d,e);A.Of(d.mode)?(d.Ff=B.Wf.dir.node,d.Hf=B.Wf.dir.stream,d.Gf={}):A.isFile(d.mode)?(d.Ff=B.Wf.file.node,d.Hf=B.Wf.file.stream,d.Lf=0,d.Gf=null):A.rg(d.mode)?(d.Ff=B.Wf.link.node,d.Hf=B.Wf.link.stream):A.wg(d.mode)&&(d.Ff=B.Wf.ah.node,d.Hf=B.Wf.ah.stream);d.timestamp=Date.now();a&&(a.Gf[c]=d,a.timestamp=d.timestamp);return d},zi:function(a){return a.Gf?a.Gf.subarray?a.Gf.subarray(0,a.Lf):new Uint8Array(a.Gf):new Uint8Array(0)},ih:function(a,c){var d=a.Gf?a.Gf.length:0;d>=c||(c=
Math.max(c,d*(1048576>d?2:1.125)>>>0),0!=d&&(c=Math.max(c,256)),d=a.Gf,a.Gf=new Uint8Array(c),0<a.Lf&&a.Gf.set(d.subarray(0,a.Lf),0))},oi:function(a,c){if(a.Lf!=c)if(0==c)a.Gf=null,a.Lf=0;else{var d=a.Gf;a.Gf=new Uint8Array(c);d&&a.Gf.set(d.subarray(0,Math.min(c,a.Lf)));a.Lf=c}},Ff:{Tf:function(a){var c={};c.dev=A.wg(a.mode)?a.id:1;c.ino=a.id;c.mode=a.mode;c.nlink=1;c.uid=0;c.gid=0;c.rdev=a.rdev;A.Of(a.mode)?c.size=4096:A.isFile(a.mode)?c.size=a.Lf:A.rg(a.mode)?c.size=a.link.length:c.size=0;c.atime=
new Date(a.timestamp);c.mtime=new Date(a.timestamp);c.ctime=new Date(a.timestamp);c.zh=4096;c.blocks=Math.ceil(c.size/c.zh);return c},Pf:function(a,c){void 0!==c.mode&&(a.mode=c.mode);void 0!==c.timestamp&&(a.timestamp=c.timestamp);void 0!==c.size&&B.oi(a,c.size)},lookup:function(){throw A.Ig[44];},$f:function(a,c,d,e){return B.createNode(a,c,d,e)},rename:function(a,c,d){if(A.Of(a.mode)){try{var e=A.Zf(c,d)}catch(h){}if(e)for(var g in e.Gf)throw new A.Ef(55);}delete a.parent.Gf[a.name];a.parent.timestamp=
Date.now();a.name=d;c.Gf[d]=a;c.timestamp=a.parent.timestamp;a.parent=c},unlink:function(a,c){delete a.Gf[c];a.timestamp=Date.now()},rmdir:function(a,c){var d=A.Zf(a,c),e;for(e in d.Gf)throw new A.Ef(55);delete a.Gf[c];a.timestamp=Date.now()},readdir:function(a){var c=[".",".."],d;for(d in a.Gf)a.Gf.hasOwnProperty(d)&&c.push(d);return c},symlink:function(a,c,d){a=B.createNode(a,c,41471,0);a.link=d;return a},readlink:function(a){if(!A.rg(a.mode))throw new A.Ef(28);return a.link}},Hf:{read:function(a,
c,d,e,g){var h=a.node.Gf;if(g>=a.node.Lf)return 0;a=Math.min(a.node.Lf-g,e);if(8<a&&h.subarray)c.set(h.subarray(g,g+a),d);else for(e=0;e<a;e++)c[d+e]=h[g+e];return a},write:function(a,c,d,e,g,h){c.buffer===p.buffer&&(h=!1);if(!e)return 0;a=a.node;a.timestamp=Date.now();if(c.subarray&&(!a.Gf||a.Gf.subarray)){if(h)return a.Gf=c.subarray(d,d+e),a.Lf=e;if(0===a.Lf&&0===g)return a.Gf=c.slice(d,d+e),a.Lf=e;if(g+e<=a.Lf)return a.Gf.set(c.subarray(d,d+e),g),e}B.ih(a,g+e);if(a.Gf.subarray&&c.subarray)a.Gf.set(c.subarray(d,
d+e),g);else for(h=0;h<e;h++)a.Gf[g+h]=c[d+h];a.Lf=Math.max(a.Lf,g+e);return e},Uf:function(a,c,d){1===d?c+=a.position:2===d&&A.isFile(a.node.mode)&&(c+=a.node.Lf);if(0>c)throw new A.Ef(28);return c},og:function(a,c,d){B.ih(a.node,c+d);a.node.Lf=Math.max(a.node.Lf,c+d)},gg:function(a,c,d,e,g){if(!A.isFile(a.node.mode))throw new A.Ef(43);a=a.node.Gf;if(g&2||a.buffer!==p.buffer){if(0<d||d+c<a.length)a.subarray?a=a.subarray(d,d+c):a=Array.prototype.slice.call(a,d,d+c);d=!0;n();c=void 0;if(!c)throw new A.Ef(48);
p.set(a,c)}else d=!1,c=a.byteOffset;return{Df:c,xh:d}},mg:function(a,c,d,e){B.Hf.write(a,c,0,e,d,!1);return 0}}};function qb(a,c,d){var e="al "+a;ka(a,g=>{g||n(`Loading data file "${a}" failed (no arrayBuffer).`);c(new Uint8Array(g));e&&Ia(e)},()=>{if(d)d();else throw`Loading data file "${a}" failed.`;});e&&Ha(e)}var rb=b.preloadPlugins||[];function sb(a,c,d,e){"undefined"!=typeof Browser&&Browser.dg();var g=!1;rb.forEach(function(h){!g&&h.canHandle(c)&&(h.handle(a,c,d,e),g=!0)});return g}
function tb(a,c){var d=0;a&&(d|=365);c&&(d|=146);return d}
var A={root:null,tg:[],gh:{},streams:[],ji:1,Vf:null,fh:"/",Pg:!1,ph:!0,Ef:null,Ig:{},Jh:null,Ag:0,Kf:(a,c={})=>{a=ib(a);if(!a)return{path:"",node:null};c=Object.assign({Gg:!0,Xg:0},c);if(8<c.Xg)throw new A.Ef(32);a=a.split("/").filter(k=>!!k);for(var d=A.root,e="/",g=0;g<a.length;g++){var h=g===a.length-1;if(h&&c.parent)break;d=A.Zf(d,a[g]);e=cb(e+"/"+a[g]);A.eg(d)&&(!h||h&&c.Gg)&&(d=d.sg.root);if(!h||c.Sf)for(h=0;A.rg(d.mode);)if(d=A.readlink(e),e=ib(db(e),d),d=A.Kf(e,{Xg:c.Xg+1}).node,40<h++)throw new A.Ef(32);
}return{path:e,node:d}},ag:a=>{for(var c;;){if(A.xg(a))return a=a.Nf.qh,c?"/"!==a[a.length-1]?a+"/"+c:a+c:a;c=c?a.name+"/"+c:a.name;a=a.parent}},Og:(a,c)=>{for(var d=0,e=0;e<c.length;e++)d=(d<<5)-d+c.charCodeAt(e)|0;return(a+d>>>0)%A.Vf.length},nh:a=>{var c=A.Og(a.parent.id,a.name);a.hg=A.Vf[c];A.Vf[c]=a},oh:a=>{var c=A.Og(a.parent.id,a.name);if(A.Vf[c]===a)A.Vf[c]=a.hg;else for(c=A.Vf[c];c;){if(c.hg===a){c.hg=a.hg;break}c=c.hg}},Zf:(a,c)=>{var d=A.hi(a);if(d)throw new A.Ef(d,a);for(d=A.Vf[A.Og(a.id,
c)];d;d=d.hg){var e=d.name;if(d.parent.id===a.id&&e===c)return d}return A.lookup(a,c)},createNode:(a,c,d,e)=>{a=new A.sh(a,c,d,e);A.nh(a);return a},Fg:a=>{A.oh(a)},xg:a=>a===a.parent,eg:a=>!!a.sg,isFile:a=>32768===(a&61440),Of:a=>16384===(a&61440),rg:a=>40960===(a&61440),wg:a=>8192===(a&61440),fi:a=>24576===(a&61440),isFIFO:a=>4096===(a&61440),isSocket:a=>49152===(a&49152),jh:a=>{var c=["r","w","rw"][a&3];a&512&&(c+="w");return c},ig:(a,c)=>{if(A.ph)return 0;if(!c.includes("r")||a.mode&292){if(c.includes("w")&&
!(a.mode&146)||c.includes("x")&&!(a.mode&73))return 2}else return 2;return 0},hi:a=>{var c=A.ig(a,"x");return c?c:a.Ff.lookup?0:2},Ug:(a,c)=>{try{return A.Zf(a,c),20}catch(d){}return A.ig(a,"wx")},yg:(a,c,d)=>{try{var e=A.Zf(a,c)}catch(g){return g.Mf}if(a=A.ig(a,"wx"))return a;if(d){if(!A.Of(e.mode))return 54;if(A.xg(e)||A.ag(e)===A.cwd())return 10}else if(A.Of(e.mode))return 31;return 0},ii:(a,c)=>a?A.rg(a.mode)?32:A.Of(a.mode)&&("r"!==A.jh(c)||c&512)?31:A.ig(a,A.jh(c)):44,uh:4096,ki:(a=0,c=A.uh)=>
{for(;a<=c;a++)if(!A.streams[a])return a;throw new A.Ef(33);},pg:a=>A.streams[a],eh:(a,c,d)=>{A.ug||(A.ug=function(){this.Qf={}},A.ug.prototype={},Object.defineProperties(A.ug.prototype,{object:{get:function(){return this.node},set:function(e){this.node=e}},flags:{get:function(){return this.Qf.flags},set:function(e){this.Qf.flags=e}},position:{get:function(){return this.Qf.position},set:function(e){this.Qf.position=e}}}));a=Object.assign(new A.ug,a);c=A.ki(c,d);a.fd=c;return A.streams[c]=a},Ch:a=>
{A.streams[a]=null},Bh:{open:a=>{a.Hf=A.Kh(a.node.rdev).Hf;a.Hf.open&&a.Hf.open(a)},Uf:()=>{throw new A.Ef(70);}},Tg:a=>a>>8,Ai:a=>a&255,fg:(a,c)=>a<<8|c,Yg:(a,c)=>{A.gh[a]={Hf:c}},Kh:a=>A.gh[a],kh:a=>{var c=[];for(a=[a];a.length;){var d=a.pop();c.push(d);a.push.apply(a,d.tg)}return c},rh:(a,c)=>{function d(k){A.Ag--;return c(k)}function e(k){if(k){if(!e.Ih)return e.Ih=!0,d(k)}else++h>=g.length&&d(null)}"function"==typeof a&&(c=a,a=!1);A.Ag++;1<A.Ag&&oa("warning: "+A.Ag+" FS.syncfs operations in flight at once, probably just doing extra work");
var g=A.kh(A.root.Nf),h=0;g.forEach(k=>{if(!k.type.rh)return e(null);k.type.rh(k,a,e)})},Nf:(a,c,d)=>{var e="/"===d,g=!d;if(e&&A.root)throw new A.Ef(10);if(!e&&!g){var h=A.Kf(d,{Gg:!1});d=h.path;h=h.node;if(A.eg(h))throw new A.Ef(10);if(!A.Of(h.mode))throw new A.Ef(54);}c={type:a,Di:c,qh:d,tg:[]};a=a.Nf(c);a.Nf=c;c.root=a;e?A.root=a:h&&(h.sg=c,h.Nf&&h.Nf.tg.push(c));return a},Gi:a=>{a=A.Kf(a,{Gg:!1});if(!A.eg(a.node))throw new A.Ef(28);a=a.node;var c=a.sg,d=A.kh(c);Object.keys(A.Vf).forEach(e=>{for(e=
A.Vf[e];e;){var g=e.hg;d.includes(e.Nf)&&A.Fg(e);e=g}});a.sg=null;a.Nf.tg.splice(a.Nf.tg.indexOf(c),1)},lookup:(a,c)=>a.Ff.lookup(a,c),$f:(a,c,d)=>{var e=A.Kf(a,{parent:!0}).node;a=eb(a);if(!a||"."===a||".."===a)throw new A.Ef(28);var g=A.Ug(e,a);if(g)throw new A.Ef(g);if(!e.Ff.$f)throw new A.Ef(63);return e.Ff.$f(e,a,c,d)},create:(a,c)=>A.$f(a,(void 0!==c?c:438)&4095|32768,0),mkdir:(a,c)=>A.$f(a,(void 0!==c?c:511)&1023|16384,0),Bi:(a,c)=>{a=a.split("/");for(var d="",e=0;e<a.length;++e)if(a[e]){d+=
"/"+a[e];try{A.mkdir(d,c)}catch(g){if(20!=g.Mf)throw g;}}},zg:(a,c,d)=>{"undefined"==typeof d&&(d=c,c=438);return A.$f(a,c|8192,d)},symlink:(a,c)=>{if(!ib(a))throw new A.Ef(44);var d=A.Kf(c,{parent:!0}).node;if(!d)throw new A.Ef(44);c=eb(c);var e=A.Ug(d,c);if(e)throw new A.Ef(e);if(!d.Ff.symlink)throw new A.Ef(63);return d.Ff.symlink(d,c,a)},rename:(a,c)=>{var d=db(a),e=db(c),g=eb(a),h=eb(c);var k=A.Kf(a,{parent:!0});var m=k.node;k=A.Kf(c,{parent:!0});k=k.node;if(!m||!k)throw new A.Ef(44);if(m.Nf!==
k.Nf)throw new A.Ef(75);var v=A.Zf(m,g);a=jb(a,e);if("."!==a.charAt(0))throw new A.Ef(28);a=jb(c,d);if("."!==a.charAt(0))throw new A.Ef(55);try{var q=A.Zf(k,h)}catch(t){}if(v!==q){c=A.Of(v.mode);if(g=A.yg(m,g,c))throw new A.Ef(g);if(g=q?A.yg(k,h,c):A.Ug(k,h))throw new A.Ef(g);if(!m.Ff.rename)throw new A.Ef(63);if(A.eg(v)||q&&A.eg(q))throw new A.Ef(10);if(k!==m&&(g=A.ig(m,"w")))throw new A.Ef(g);A.oh(v);try{m.Ff.rename(v,k,h)}catch(t){throw t;}finally{A.nh(v)}}},rmdir:a=>{var c=A.Kf(a,{parent:!0}).node;
a=eb(a);var d=A.Zf(c,a),e=A.yg(c,a,!0);if(e)throw new A.Ef(e);if(!c.Ff.rmdir)throw new A.Ef(63);if(A.eg(d))throw new A.Ef(10);c.Ff.rmdir(c,a);A.Fg(d)},readdir:a=>{a=A.Kf(a,{Sf:!0}).node;if(!a.Ff.readdir)throw new A.Ef(54);return a.Ff.readdir(a)},unlink:a=>{var c=A.Kf(a,{parent:!0}).node;if(!c)throw new A.Ef(44);a=eb(a);var d=A.Zf(c,a),e=A.yg(c,a,!1);if(e)throw new A.Ef(e);if(!c.Ff.unlink)throw new A.Ef(63);if(A.eg(d))throw new A.Ef(10);c.Ff.unlink(c,a);A.Fg(d)},readlink:a=>{a=A.Kf(a).node;if(!a)throw new A.Ef(44);
if(!a.Ff.readlink)throw new A.Ef(28);return ib(A.ag(a.parent),a.Ff.readlink(a))},stat:(a,c)=>{a=A.Kf(a,{Sf:!c}).node;if(!a)throw new A.Ef(44);if(!a.Ff.Tf)throw new A.Ef(63);return a.Ff.Tf(a)},lstat:a=>A.stat(a,!0),chmod:(a,c,d)=>{a="string"==typeof a?A.Kf(a,{Sf:!d}).node:a;if(!a.Ff.Pf)throw new A.Ef(63);a.Ff.Pf(a,{mode:c&4095|a.mode&-4096,timestamp:Date.now()})},lchmod:(a,c)=>{A.chmod(a,c,!0)},fchmod:(a,c)=>{a=A.pg(a);if(!a)throw new A.Ef(8);A.chmod(a.node,c)},chown:(a,c,d,e)=>{a="string"==typeof a?
A.Kf(a,{Sf:!e}).node:a;if(!a.Ff.Pf)throw new A.Ef(63);a.Ff.Pf(a,{timestamp:Date.now()})},lchown:(a,c,d)=>{A.chown(a,c,d,!0)},fchown:(a,c,d)=>{a=A.pg(a);if(!a)throw new A.Ef(8);A.chown(a.node,c,d)},truncate:(a,c)=>{if(0>c)throw new A.Ef(28);a="string"==typeof a?A.Kf(a,{Sf:!0}).node:a;if(!a.Ff.Pf)throw new A.Ef(63);if(A.Of(a.mode))throw new A.Ef(31);if(!A.isFile(a.mode))throw new A.Ef(28);var d=A.ig(a,"w");if(d)throw new A.Ef(d);a.Ff.Pf(a,{size:c,timestamp:Date.now()})},yi:(a,c)=>{a=A.pg(a);if(!a)throw new A.Ef(8);
if(0===(a.flags&2097155))throw new A.Ef(28);A.truncate(a.node,c)},Hi:(a,c,d)=>{a=A.Kf(a,{Sf:!0}).node;a.Ff.Pf(a,{timestamp:Math.max(c,d)})},open:(a,c,d)=>{if(""===a)throw new A.Ef(44);if("string"==typeof c){var e={r:0,"r+":2,w:577,"w+":578,a:1089,"a+":1090}[c];if("undefined"==typeof e)throw Error("Unknown file open mode: "+c);c=e}d=c&64?("undefined"==typeof d?438:d)&4095|32768:0;if("object"==typeof a)var g=a;else{a=cb(a);try{g=A.Kf(a,{Sf:!(c&131072)}).node}catch(h){}}e=!1;if(c&64)if(g){if(c&128)throw new A.Ef(20);
}else g=A.$f(a,d,0),e=!0;if(!g)throw new A.Ef(44);A.wg(g.mode)&&(c&=-513);if(c&65536&&!A.Of(g.mode))throw new A.Ef(54);if(!e&&(d=A.ii(g,c)))throw new A.Ef(d);c&512&&!e&&A.truncate(g,0);c&=-131713;g=A.eh({node:g,path:A.ag(g),flags:c,seekable:!0,position:0,Hf:g.Hf,wi:[],error:!1});g.Hf.open&&g.Hf.open(g);!b.logReadFiles||c&1||(A.Wg||(A.Wg={}),a in A.Wg||(A.Wg[a]=1));return g},close:a=>{if(A.qg(a))throw new A.Ef(8);a.Ng&&(a.Ng=null);try{a.Hf.close&&a.Hf.close(a)}catch(c){throw c;}finally{A.Ch(a.fd)}a.fd=
null},qg:a=>null===a.fd,Uf:(a,c,d)=>{if(A.qg(a))throw new A.Ef(8);if(!a.seekable||!a.Hf.Uf)throw new A.Ef(70);if(0!=d&&1!=d&&2!=d)throw new A.Ef(28);a.position=a.Hf.Uf(a,c,d);a.wi=[];return a.position},read:(a,c,d,e,g)=>{if(0>e||0>g)throw new A.Ef(28);if(A.qg(a))throw new A.Ef(8);if(1===(a.flags&2097155))throw new A.Ef(8);if(A.Of(a.node.mode))throw new A.Ef(31);if(!a.Hf.read)throw new A.Ef(28);var h="undefined"!=typeof g;if(!h)g=a.position;else if(!a.seekable)throw new A.Ef(70);c=a.Hf.read(a,c,d,
e,g);h||(a.position+=c);return c},write:(a,c,d,e,g,h)=>{if(0>e||0>g)throw new A.Ef(28);if(A.qg(a))throw new A.Ef(8);if(0===(a.flags&2097155))throw new A.Ef(8);if(A.Of(a.node.mode))throw new A.Ef(31);if(!a.Hf.write)throw new A.Ef(28);a.seekable&&a.flags&1024&&A.Uf(a,0,2);var k="undefined"!=typeof g;if(!k)g=a.position;else if(!a.seekable)throw new A.Ef(70);c=a.Hf.write(a,c,d,e,g,h);k||(a.position+=c);return c},og:(a,c,d)=>{if(A.qg(a))throw new A.Ef(8);if(0>c||0>=d)throw new A.Ef(28);if(0===(a.flags&
2097155))throw new A.Ef(8);if(!A.isFile(a.node.mode)&&!A.Of(a.node.mode))throw new A.Ef(43);if(!a.Hf.og)throw new A.Ef(138);a.Hf.og(a,c,d)},gg:(a,c,d,e,g)=>{if(0!==(e&2)&&0===(g&2)&&2!==(a.flags&2097155))throw new A.Ef(2);if(1===(a.flags&2097155))throw new A.Ef(2);if(!a.Hf.gg)throw new A.Ef(43);return a.Hf.gg(a,c,d,e,g)},mg:(a,c,d,e,g)=>a.Hf.mg?a.Hf.mg(a,c,d,e,g):0,Ci:()=>0,Qg:(a,c,d)=>{if(!a.Hf.Qg)throw new A.Ef(59);return a.Hf.Qg(a,c,d)},readFile:(a,c={})=>{c.flags=c.flags||0;c.encoding=c.encoding||
"binary";if("utf8"!==c.encoding&&"binary"!==c.encoding)throw Error('Invalid encoding type "'+c.encoding+'"');var d,e=A.open(a,c.flags);a=A.stat(a).size;var g=new Uint8Array(a);A.read(e,g,0,a,0);"utf8"===c.encoding?d=Wa(g,0):"binary"===c.encoding&&(d=g);A.close(e);return d},writeFile:(a,c,d={})=>{d.flags=d.flags||577;a=A.open(a,d.flags,d.mode);if("string"==typeof c){var e=new Uint8Array(Ta(c)+1);c=Ua(c,e,0,e.length);A.write(a,e,0,c,void 0,d.Ah)}else if(ArrayBuffer.isView(c))A.write(a,c,0,c.byteLength,
void 0,d.Ah);else throw Error("Unsupported data type");A.close(a)},cwd:()=>A.fh,chdir:a=>{a=A.Kf(a,{Sf:!0});if(null===a.node)throw new A.Ef(44);if(!A.Of(a.node.mode))throw new A.Ef(54);var c=A.ig(a.node,"x");if(c)throw new A.Ef(c);A.fh=a.path},Eh:()=>{A.mkdir("/tmp");A.mkdir("/home");A.mkdir("/home/<USER>")},Dh:()=>{A.mkdir("/dev");A.Yg(A.fg(1,3),{read:()=>0,write:(e,g,h,k)=>k});A.zg("/dev/null",A.fg(1,3));mb(A.fg(5,0),ob);mb(A.fg(6,0),pb);A.zg("/dev/tty",A.fg(5,0));A.zg("/dev/tty1",A.fg(6,0));
var a=new Uint8Array(1024),c=0,d=()=>{0===c&&(c=hb(a).byteLength);return a[--c]};A.Rf("/dev","random",d);A.Rf("/dev","urandom",d);A.mkdir("/dev/shm");A.mkdir("/dev/shm/tmp")},Gh:()=>{A.mkdir("/proc");var a=A.mkdir("/proc/self");A.mkdir("/proc/self/fd");A.Nf({Nf:()=>{var c=A.createNode(a,"fd",16895,73);c.Ff={lookup:(d,e)=>{var g=A.pg(+e);if(!g)throw new A.Ef(8);d={parent:null,Nf:{qh:"fake"},Ff:{readlink:()=>g.path}};return d.parent=d}};return c}},{},"/proc/self/fd")},Hh:()=>{b.stdin?A.Rf("/dev","stdin",
b.stdin):A.symlink("/dev/tty","/dev/stdin");b.stdout?A.Rf("/dev","stdout",null,b.stdout):A.symlink("/dev/tty","/dev/stdout");b.stderr?A.Rf("/dev","stderr",null,b.stderr):A.symlink("/dev/tty1","/dev/stderr");A.open("/dev/stdin",0);A.open("/dev/stdout",1);A.open("/dev/stderr",1)},hh:()=>{A.Ef||(A.Ef=function(a,c){this.name="ErrnoError";this.node=c;this.pi=function(d){this.Mf=d};this.pi(a);this.message="FS error"},A.Ef.prototype=Error(),A.Ef.prototype.constructor=A.Ef,[44].forEach(a=>{A.Ig[a]=new A.Ef(a);
A.Ig[a].stack="<generic error, no stack>"}))},ri:()=>{A.hh();A.Vf=Array(4096);A.Nf(B,{},"/");A.Eh();A.Dh();A.Gh();A.Jh={MEMFS:B}},dg:(a,c,d)=>{A.dg.Pg=!0;A.hh();b.stdin=a||b.stdin;b.stdout=c||b.stdout;b.stderr=d||b.stderr;A.Hh()},Ei:()=>{A.dg.Pg=!1;for(var a=0;a<A.streams.length;a++){var c=A.streams[a];c&&A.close(c)}},xi:(a,c)=>{a=A.yh(a,c);return a.exists?a.object:null},yh:(a,c)=>{try{var d=A.Kf(a,{Sf:!c});a=d.path}catch(g){}var e={xg:!1,exists:!1,error:0,name:null,path:null,object:null,li:!1,ni:null,
mi:null};try{d=A.Kf(a,{parent:!0}),e.li=!0,e.ni=d.path,e.mi=d.node,e.name=eb(a),d=A.Kf(a,{Sf:!c}),e.exists=!0,e.path=d.path,e.object=d.node,e.name=d.node.name,e.xg="/"===d.path}catch(g){e.error=g.Mf}return e},Eg:(a,c)=>{a="string"==typeof a?a:A.ag(a);for(c=c.split("/").reverse();c.length;){var d=c.pop();if(d){var e=cb(a+"/"+d);try{A.mkdir(e)}catch(g){}a=e}}return e},Fh:(a,c,d,e,g)=>{a="string"==typeof a?a:A.ag(a);c=cb(a+"/"+c);return A.create(c,tb(e,g))},vg:(a,c,d,e,g,h)=>{var k=c;a&&(a="string"==
typeof a?a:A.ag(a),k=c?cb(a+"/"+c):a);a=tb(e,g);k=A.create(k,a);if(d){if("string"==typeof d){c=Array(d.length);e=0;for(g=d.length;e<g;++e)c[e]=d.charCodeAt(e);d=c}A.chmod(k,a|146);c=A.open(k,577);A.write(c,d,0,d.length,0,h);A.close(c);A.chmod(k,a)}return k},Rf:(a,c,d,e)=>{a=fb("string"==typeof a?a:A.ag(a),c);c=tb(!!d,!!e);A.Rf.Tg||(A.Rf.Tg=64);var g=A.fg(A.Rf.Tg++,0);A.Yg(g,{open:h=>{h.seekable=!1},close:()=>{e&&e.buffer&&e.buffer.length&&e(10)},read:(h,k,m,v)=>{for(var q=0,t=0;t<v;t++){try{var F=
d()}catch(U){throw new A.Ef(29);}if(void 0===F&&0===q)throw new A.Ef(6);if(null===F||void 0===F)break;q++;k[m+t]=F}q&&(h.node.timestamp=Date.now());return q},write:(h,k,m,v)=>{for(var q=0;q<v;q++)try{e(k[m+q])}catch(t){throw new A.Ef(29);}v&&(h.node.timestamp=Date.now());return q}});return A.zg(a,c,g)},Hg:a=>{if(a.Rg||a.gi||a.link||a.Gf)return!0;if("undefined"!=typeof XMLHttpRequest)throw Error("Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.");
if(ja)try{a.Gf=kb(ja(a.url),!0),a.Lf=a.Gf.length}catch(c){throw new A.Ef(29);}else throw Error("Cannot load without read() or XMLHttpRequest.");},bh:(a,c,d,e,g)=>{function h(){this.Sg=!1;this.Qf=[]}h.prototype.get=function(q){if(!(q>this.length-1||0>q)){var t=q%this.chunkSize;return this.mh(q/this.chunkSize|0)[t]}};h.prototype.Dg=function(q){this.mh=q};h.prototype.$g=function(){var q=new XMLHttpRequest;q.open("HEAD",d,!1);q.send(null);if(!(200<=q.status&&300>q.status||304===q.status))throw Error("Couldn't load "+
d+". Status: "+q.status);var t=Number(q.getResponseHeader("Content-length")),F,U=(F=q.getResponseHeader("Accept-Ranges"))&&"bytes"===F;q=(F=q.getResponseHeader("Content-Encoding"))&&"gzip"===F;var l=1048576;U||(l=t);var w=this;w.Dg(E=>{var V=E*l,qa=(E+1)*l-1;qa=Math.min(qa,t-1);if("undefined"==typeof w.Qf[E]){var Uh=w.Qf;if(V>qa)throw Error("invalid range ("+V+", "+qa+") or no bytes requested!");if(qa>t-1)throw Error("only "+t+" bytes available! programmer error!");var W=new XMLHttpRequest;W.open("GET",
d,!1);t!==l&&W.setRequestHeader("Range","bytes="+V+"-"+qa);W.responseType="arraybuffer";W.overrideMimeType&&W.overrideMimeType("text/plain; charset=x-user-defined");W.send(null);if(!(200<=W.status&&300>W.status||304===W.status))throw Error("Couldn't load "+d+". Status: "+W.status);V=void 0!==W.response?new Uint8Array(W.response||[]):kb(W.responseText||"",!0);Uh[E]=V}if("undefined"==typeof w.Qf[E])throw Error("doXHR failed!");return w.Qf[E]});if(q||!t)l=t=1,l=t=this.mh(0).length,na("LazyFiles on gzip forces download of the whole file when length is accessed");
this.wh=t;this.vh=l;this.Sg=!0};if("undefined"!=typeof XMLHttpRequest){if(!ha)throw"Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc";var k=new h;Object.defineProperties(k,{length:{get:function(){this.Sg||this.$g();return this.wh}},chunkSize:{get:function(){this.Sg||this.$g();return this.vh}}});k={Rg:!1,Gf:k}}else k={Rg:!1,url:d};var m=A.Fh(a,c,k,e,g);k.Gf?m.Gf=k.Gf:k.url&&(m.Gf=null,m.url=k.url);Object.defineProperties(m,{Lf:{get:function(){return this.Gf.length}}});
var v={};Object.keys(m.Hf).forEach(q=>{var t=m.Hf[q];v[q]=function(){A.Hg(m);return t.apply(null,arguments)}});v.read=(q,t,F,U,l)=>{A.Hg(m);q=q.node.Gf;if(l>=q.length)t=0;else{U=Math.min(q.length-l,U);if(q.slice)for(var w=0;w<U;w++)t[F+w]=q[l+w];else for(w=0;w<U;w++)t[F+w]=q.get(l+w);t=U}return t};v.gg=()=>{A.Hg(m);n();throw new A.Ef(48);};m.Hf=v;return m}};
function ub(a,c,d){if("/"===c.charAt(0))return c;a=-100===a?A.cwd():vb(a).path;if(0==c.length){if(!d)throw new A.Ef(44);return a}return cb(a+"/"+c)}
function wb(a,c,d){try{var e=a(c)}catch(h){if(h&&h.node&&cb(c)!==cb(A.ag(h.node)))return-54;throw h;}r[d>>2]=e.dev;r[d+8>>2]=e.ino;r[d+12>>2]=e.mode;u[d+16>>2]=e.nlink;r[d+20>>2]=e.uid;r[d+24>>2]=e.gid;r[d+28>>2]=e.rdev;y=[e.size>>>0,(x=e.size,1<=+Math.abs(x)?0<x?+Math.floor(x/4294967296)>>>0:~~+Math.ceil((x-+(~~x>>>0))/4294967296)>>>0:0)];r[d+40>>2]=y[0];r[d+44>>2]=y[1];r[d+48>>2]=4096;r[d+52>>2]=e.blocks;a=e.atime.getTime();c=e.mtime.getTime();var g=e.ctime.getTime();y=[Math.floor(a/1E3)>>>0,(x=
Math.floor(a/1E3),1<=+Math.abs(x)?0<x?+Math.floor(x/4294967296)>>>0:~~+Math.ceil((x-+(~~x>>>0))/4294967296)>>>0:0)];r[d+56>>2]=y[0];r[d+60>>2]=y[1];u[d+64>>2]=a%1E3*1E3;y=[Math.floor(c/1E3)>>>0,(x=Math.floor(c/1E3),1<=+Math.abs(x)?0<x?+Math.floor(x/4294967296)>>>0:~~+Math.ceil((x-+(~~x>>>0))/4294967296)>>>0:0)];r[d+72>>2]=y[0];r[d+76>>2]=y[1];u[d+80>>2]=c%1E3*1E3;y=[Math.floor(g/1E3)>>>0,(x=Math.floor(g/1E3),1<=+Math.abs(x)?0<x?+Math.floor(x/4294967296)>>>0:~~+Math.ceil((x-+(~~x>>>0))/4294967296)>>>
0:0)];r[d+88>>2]=y[0];r[d+92>>2]=y[1];u[d+96>>2]=g%1E3*1E3;y=[e.ino>>>0,(x=e.ino,1<=+Math.abs(x)?0<x?+Math.floor(x/4294967296)>>>0:~~+Math.ceil((x-+(~~x>>>0))/4294967296)>>>0:0)];r[d+104>>2]=y[0];r[d+108>>2]=y[1];return 0}var xb=void 0;function yb(){xb+=4;return r[xb-4>>2]}function vb(a){a=A.pg(a);if(!a)throw new A.Ef(8);return a}function zb(){oa("missing function: setThrew");n(-1)}function Ab(a){return 0===a%4&&(0!==a%100||0===a%400)}
var Bb=[0,31,60,91,121,152,182,213,244,274,305,335],Cb=[0,31,59,90,120,151,181,212,243,273,304,334];function Db(a){return(Ab(a.getFullYear())?Bb:Cb)[a.getMonth()]+a.getDate()-1}function Eb(a){var c=Ta(a)+1,d=Fb(c);d&&Ua(a,ta,d,c);return d}var Gb=[],Hb;Hb=ia?()=>{var a=process.hrtime();return 1E3*a[0]+a[1]/1E6}:()=>performance.now();var Ib={};
function Jb(){if(!Kb){var a={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"==typeof navigator&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:da||"./this.program"},c;for(c in Ib)void 0===Ib[c]?delete a[c]:a[c]=Ib[c];var d=[];for(c in a)d.push(c+"="+a[c]);Kb=d}return Kb}var Kb,Lb=[31,29,31,30,31,30,31,31,30,31,30,31],Mb=[31,28,31,30,31,30,31,31,30,31,30,31];
function Nb(a,c,d,e){function g(l,w,E){for(l="number"==typeof l?l.toString():l||"";l.length<w;)l=E[0]+l;return l}function h(l,w){return g(l,w,"0")}function k(l,w){function E(qa){return 0>qa?-1:0<qa?1:0}var V;0===(V=E(l.getFullYear()-w.getFullYear()))&&0===(V=E(l.getMonth()-w.getMonth()))&&(V=E(l.getDate()-w.getDate()));return V}function m(l){switch(l.getDay()){case 0:return new Date(l.getFullYear()-1,11,29);case 1:return l;case 2:return new Date(l.getFullYear(),0,3);case 3:return new Date(l.getFullYear(),
0,2);case 4:return new Date(l.getFullYear(),0,1);case 5:return new Date(l.getFullYear()-1,11,31);case 6:return new Date(l.getFullYear()-1,11,30)}}function v(l){var w=l.kg;for(l=new Date((new Date(l.lg+1900,0,1)).getTime());0<w;){var E=l.getMonth(),V=(Ab(l.getFullYear())?Lb:Mb)[E];if(w>V-l.getDate())w-=V-l.getDate()+1,l.setDate(1),11>E?l.setMonth(E+1):(l.setMonth(0),l.setFullYear(l.getFullYear()+1));else{l.setDate(l.getDate()+w);break}}E=new Date(l.getFullYear()+1,0,4);w=m(new Date(l.getFullYear(),
0,4));E=m(E);return 0>=k(w,l)?0>=k(E,l)?l.getFullYear()+1:l.getFullYear():l.getFullYear()-1}var q=r[e+40>>2];e={ui:r[e>>2],ti:r[e+4>>2],Bg:r[e+8>>2],Zg:r[e+12>>2],Cg:r[e+16>>2],lg:r[e+20>>2],Xf:r[e+24>>2],kg:r[e+28>>2],Fi:r[e+32>>2],si:r[e+36>>2],vi:q?z(q):""};d=z(d);q={"%c":"%a %b %d %H:%M:%S %Y","%D":"%m/%d/%y","%F":"%Y-%m-%d","%h":"%b","%r":"%I:%M:%S %p","%R":"%H:%M","%T":"%H:%M:%S","%x":"%m/%d/%y","%X":"%H:%M:%S","%Ec":"%c","%EC":"%C","%Ex":"%m/%d/%y","%EX":"%H:%M:%S","%Ey":"%y","%EY":"%Y","%Od":"%d",
"%Oe":"%e","%OH":"%H","%OI":"%I","%Om":"%m","%OM":"%M","%OS":"%S","%Ou":"%u","%OU":"%U","%OV":"%V","%Ow":"%w","%OW":"%W","%Oy":"%y"};for(var t in q)d=d.replace(new RegExp(t,"g"),q[t]);var F="Sunday Monday Tuesday Wednesday Thursday Friday Saturday".split(" "),U="January February March April May June July August September October November December".split(" ");q={"%a":function(l){return F[l.Xf].substring(0,3)},"%A":function(l){return F[l.Xf]},"%b":function(l){return U[l.Cg].substring(0,3)},"%B":function(l){return U[l.Cg]},
"%C":function(l){return h((l.lg+1900)/100|0,2)},"%d":function(l){return h(l.Zg,2)},"%e":function(l){return g(l.Zg,2," ")},"%g":function(l){return v(l).toString().substring(2)},"%G":function(l){return v(l)},"%H":function(l){return h(l.Bg,2)},"%I":function(l){l=l.Bg;0==l?l=12:12<l&&(l-=12);return h(l,2)},"%j":function(l){for(var w=0,E=0;E<=l.Cg-1;w+=(Ab(l.lg+1900)?Lb:Mb)[E++]);return h(l.Zg+w,3)},"%m":function(l){return h(l.Cg+1,2)},"%M":function(l){return h(l.ti,2)},"%n":function(){return"\n"},"%p":function(l){return 0<=
l.Bg&&12>l.Bg?"AM":"PM"},"%S":function(l){return h(l.ui,2)},"%t":function(){return"\t"},"%u":function(l){return l.Xf||7},"%U":function(l){return h(Math.floor((l.kg+7-l.Xf)/7),2)},"%V":function(l){var w=Math.floor((l.kg+7-(l.Xf+6)%7)/7);2>=(l.Xf+371-l.kg-2)%7&&w++;if(w)53==w&&(E=(l.Xf+371-l.kg)%7,4==E||3==E&&Ab(l.lg)||(w=1));else{w=52;var E=(l.Xf+7-l.kg-1)%7;(4==E||5==E&&Ab(l.lg%400-1))&&w++}return h(w,2)},"%w":function(l){return l.Xf},"%W":function(l){return h(Math.floor((l.kg+7-(l.Xf+6)%7)/7),2)},
"%y":function(l){return(l.lg+1900).toString().substring(2)},"%Y":function(l){return l.lg+1900},"%z":function(l){l=l.si;var w=0<=l;l=Math.abs(l)/60;return(w?"+":"-")+String("0000"+(l/60*100+l%60)).slice(-4)},"%Z":function(l){return l.vi},"%%":function(){return"%"}};d=d.replace(/%%/g,"\x00\x00");for(t in q)d.includes(t)&&(d=d.replace(new RegExp(t,"g"),q[t](e)));d=d.replace(/\0\0/g,"%");t=kb(d,!1);if(t.length>c)return 0;p.set(t,a);return t.length-1}var Ob=[];
function Pb(a){var c=Ob[a];c||(a>=Ob.length&&(Ob.length=a+1),Ob[a]=c=ya.get(a));return c}function Qb(a,c,d,e){a||(a=this);this.parent=a;this.Nf=a.Nf;this.sg=null;this.id=A.ji++;this.name=c;this.mode=d;this.Ff={};this.Hf={};this.rdev=e}
Object.defineProperties(Qb.prototype,{read:{get:function(){return 365===(this.mode&365)},set:function(a){a?this.mode|=365:this.mode&=-366}},write:{get:function(){return 146===(this.mode&146)},set:function(a){a?this.mode|=146:this.mode&=-147}},gi:{get:function(){return A.Of(this.mode)}},Rg:{get:function(){return A.wg(this.mode)}}});A.sh=Qb;
A.dh=function(a,c,d,e,g,h,k,m,v,q){function t(l){function w(E){q&&q();m||A.vg(a,c,E,e,g,v);h&&h();Ia(U)}sb(l,F,w,()=>{k&&k();Ia(U)})||w(l)}var F=c?ib(cb(a+"/"+c)):a,U="cp "+F;Ha(U);"string"==typeof d?qb(d,l=>t(l),k):t(d)};A.ri();b.FS_createPath=A.Eg;b.FS_createDataFile=A.vg;b.FS_createPreloadedFile=A.dh;b.FS_unlink=A.unlink;b.FS_createLazyFile=A.bh;b.FS_createDevice=A.Rf;
var cc={a:function(a,c,d,e){n(`Assertion failed: ${z(a)}, at: `+[c?z(c):"unknown filename",d,e?z(e):"unknown function"])},n:function(a,c,d){(new Za(a)).dg(c,d);$a=a;ab++;throw $a;},t:function(a,c,d){xb=d;try{var e=vb(a);switch(c){case 0:var g=yb();return 0>g?-28:A.eh(e,g).fd;case 1:case 2:return 0;case 3:return e.flags;case 4:return g=yb(),e.flags|=g,0;case 5:return g=yb(),ua[g+0>>1]=2,0;case 6:case 7:return 0;case 16:case 8:return-28;case 9:return r[Rb()>>2]=28,-1;default:return-28}}catch(h){if("undefined"==
typeof A||"ErrnoError"!==h.name)throw h;return-h.Mf}},N:function(a,c){try{var d=vb(a);return wb(A.stat,d.path,c)}catch(e){if("undefined"==typeof A||"ErrnoError"!==e.name)throw e;return-e.Mf}},K:function(a,c){try{if(0===c)return-28;var d=A.cwd(),e=Ta(d)+1;if(c<e)return-68;Ua(d,ta,a,c);return e}catch(g){if("undefined"==typeof A||"ErrnoError"!==g.name)throw g;return-g.Mf}},R:function(a,c,d){xb=d;try{var e=vb(a);switch(c){case 21509:case 21505:return e.tty?0:-59;case 21510:case 21511:case 21512:case 21506:case 21507:case 21508:return e.tty?
0:-59;case 21519:if(!e.tty)return-59;var g=yb();return r[g>>2]=0;case 21520:return e.tty?-28:-59;case 21531:return g=yb(),A.Qg(e,c,g);case 21523:return e.tty?0:-59;case 21524:return e.tty?0:-59;default:return-28}}catch(h){if("undefined"==typeof A||"ErrnoError"!==h.name)throw h;return-h.Mf}},L:function(a,c,d,e){try{c=z(c);var g=e&256;c=ub(a,c,e&4096);return wb(g?A.lstat:A.stat,c,d)}catch(h){if("undefined"==typeof A||"ErrnoError"!==h.name)throw h;return-h.Mf}},r:function(a,c,d,e){xb=e;try{c=z(c);c=
ub(a,c);var g=e?yb():0;return A.open(c,d,g).fd}catch(h){if("undefined"==typeof A||"ErrnoError"!==h.name)throw h;return-h.Mf}},B:function(a){try{return a=z(a),A.rmdir(a),0}catch(c){if("undefined"==typeof A||"ErrnoError"!==c.name)throw c;return-c.Mf}},M:function(a,c){try{return a=z(a),wb(A.stat,a,c)}catch(d){if("undefined"==typeof A||"ErrnoError"!==d.name)throw d;return-d.Mf}},C:function(a,c,d){try{return c=z(c),c=ub(a,c),0===d?A.unlink(c):512===d?A.rmdir(c):n("Invalid flags passed to unlinkat"),0}catch(e){if("undefined"==
typeof A||"ErrnoError"!==e.name)throw e;return-e.Mf}},S:function(a){do{var c=u[a>>2];a+=4;var d=u[a>>2];a+=4;var e=u[a>>2];a+=4;c=z(c);A.Eg("/",db(c),!0,!0);A.vg(c,null,p.subarray(e,e+d),!0,!0,!0)}while(u[a>>2])},P:function(){return!0},y:function(){throw Infinity;},F:function(a,c){a=new Date(1E3*(u[a>>2]+4294967296*r[a+4>>2]));r[c>>2]=a.getUTCSeconds();r[c+4>>2]=a.getUTCMinutes();r[c+8>>2]=a.getUTCHours();r[c+12>>2]=a.getUTCDate();r[c+16>>2]=a.getUTCMonth();r[c+20>>2]=a.getUTCFullYear()-1900;r[c+
24>>2]=a.getUTCDay();r[c+28>>2]=(a.getTime()-Date.UTC(a.getUTCFullYear(),0,1,0,0,0,0))/864E5|0},G:function(a,c){a=new Date(1E3*(u[a>>2]+4294967296*r[a+4>>2]));r[c>>2]=a.getSeconds();r[c+4>>2]=a.getMinutes();r[c+8>>2]=a.getHours();r[c+12>>2]=a.getDate();r[c+16>>2]=a.getMonth();r[c+20>>2]=a.getFullYear()-1900;r[c+24>>2]=a.getDay();r[c+28>>2]=Db(a)|0;r[c+36>>2]=-(60*a.getTimezoneOffset());var d=(new Date(a.getFullYear(),6,1)).getTimezoneOffset(),e=(new Date(a.getFullYear(),0,1)).getTimezoneOffset();
r[c+32>>2]=(d!=e&&a.getTimezoneOffset()==Math.min(e,d))|0},H:function(a){var c=new Date(r[a+20>>2]+1900,r[a+16>>2],r[a+12>>2],r[a+8>>2],r[a+4>>2],r[a>>2],0),d=r[a+32>>2],e=c.getTimezoneOffset(),g=(new Date(c.getFullYear(),6,1)).getTimezoneOffset(),h=(new Date(c.getFullYear(),0,1)).getTimezoneOffset(),k=Math.min(h,g);0>d?r[a+32>>2]=Number(g!=h&&k==e):0<d!=(k==e)&&(g=Math.max(h,g),c.setTime(c.getTime()+6E4*((0<d?k:g)-e)));r[a+24>>2]=c.getDay();r[a+28>>2]=Db(c)|0;r[a>>2]=c.getSeconds();r[a+4>>2]=c.getMinutes();
r[a+8>>2]=c.getHours();r[a+12>>2]=c.getDate();r[a+16>>2]=c.getMonth();r[a+20>>2]=c.getYear();return c.getTime()/1E3|0},D:function(a,c,d,e,g,h,k){try{var m=vb(e),v=A.gg(m,a,g,c,d),q=v.Df;r[h>>2]=v.xh;u[k>>2]=q;return 0}catch(t){if("undefined"==typeof A||"ErrnoError"!==t.name)throw t;return-t.Mf}},E:function(a,c,d,e,g,h){try{var k=vb(g);if(d&2){if(!A.isFile(k.node.mode))throw new A.Ef(43);if(!(e&2)){var m=ta.slice(a,a+c);A.mg(k,m,h,c,e)}}}catch(v){if("undefined"==typeof A||"ErrnoError"!==v.name)throw v;
return-v.Mf}},A:function(a,c,d){function e(v){return(v=v.toTimeString().match(/\(([A-Za-z ]+)\)$/))?v[1]:"GMT"}var g=(new Date).getFullYear(),h=new Date(g,0,1),k=new Date(g,6,1);g=h.getTimezoneOffset();var m=k.getTimezoneOffset();u[a>>2]=60*Math.max(g,m);r[c>>2]=Number(g!=m);a=e(h);c=e(k);a=Eb(a);c=Eb(c);m<g?(u[d>>2]=a,u[d+4>>2]=c):(u[d>>2]=c,u[d+4>>2]=a)},l:function(){n("")},q:function(a,c,d){Gb.length=0;var e;for(d>>=2;e=ta[c++];)d+=105!=e&d,Gb.push(105==e?r[d]:wa[d++>>1]),++d;return Qa[a].apply(null,
Gb)},m:function(){return Date.now()},O:Hb,Q:function(a,c,d){ta.copyWithin(a,c,c+d)},z:function(a){var c=ta.length;a>>>=0;if(2147483648<a)return!1;for(var d=1;4>=d;d*=2){var e=c*(1+.2/d);e=Math.min(e,a+100663296);var g=Math,h=g.min;e=Math.max(a,e);e+=(65536-e%65536)%65536;a:{var k=ra.buffer;try{ra.grow(h.call(g,2147483648,e)-k.byteLength+65535>>>16);xa();var m=1;break a}catch(v){}m=void 0}if(m)return!0}return!1},I:function(a,c){var d=0;Jb().forEach(function(e,g){var h=c+d;g=u[a+4*g>>2]=h;for(h=0;h<
e.length;++h)p[g++>>0]=e.charCodeAt(h);p[g>>0]=0;d+=e.length+1});return 0},J:function(a,c){var d=Jb();u[a>>2]=d.length;var e=0;d.forEach(function(g){e+=g.length+1});u[c>>2]=e;return 0},k:function(a){if(!noExitRuntime){if(b.onExit)b.onExit(a);sa=!0}ea(a,new Ra(a))},p:function(a){try{var c=vb(a);A.close(c);return 0}catch(d){if("undefined"==typeof A||"ErrnoError"!==d.name)throw d;return d.Mf}},s:function(a,c,d,e){try{a:{var g=vb(a);a=c;for(var h,k=c=0;k<d;k++){var m=u[a>>2],v=u[a+4>>2];a+=8;var q=A.read(g,
p,m,v,h);if(0>q){var t=-1;break a}c+=q;if(q<v)break;"undefined"!==typeof h&&(h+=q)}t=c}u[e>>2]=t;return 0}catch(F){if("undefined"==typeof A||"ErrnoError"!==F.name)throw F;return F.Mf}},w:function(a,c,d,e,g){try{c=d+2097152>>>0<4194305-!!c?(c>>>0)+4294967296*d:NaN;if(isNaN(c))return 61;var h=vb(a);A.Uf(h,c,e);y=[h.position>>>0,(x=h.position,1<=+Math.abs(x)?0<x?+Math.floor(x/4294967296)>>>0:~~+Math.ceil((x-+(~~x>>>0))/4294967296)>>>0:0)];r[g>>2]=y[0];r[g+4>>2]=y[1];h.Ng&&0===c&&0===e&&(h.Ng=null);return 0}catch(k){if("undefined"==
typeof A||"ErrnoError"!==k.name)throw k;return k.Mf}},o:function(a,c,d,e){try{a:{var g=vb(a);a=c;for(var h,k=c=0;k<d;k++){var m=u[a>>2],v=u[a+4>>2];a+=8;var q=A.write(g,p,m,v,h);if(0>q){var t=-1;break a}c+=q;"undefined"!==typeof h&&(h+=q)}t=c}u[e>>2]=t;return 0}catch(F){if("undefined"==typeof A||"ErrnoError"!==F.name)throw F;return F.Mf}},c:Sb,e:Tb,b:Ub,h:Vb,i:Wb,d:Xb,f:Yb,g:Zb,j:$b,u:ac,v:bc,T:Nb,x:function(a,c,d,e){return Nb(a,c,d,e)}};
(function(){function a(d){d=d.exports;b.asm=d;ra=b.asm.U;xa();ya=b.asm.rf;Aa.unshift(b.asm.V);Ia("wasm-instantiate");return d}var c={a:cc};Ha("wasm-instantiate");if(b.instantiateWasm)try{return b.instantiateWasm(c,a)}catch(d){oa("Module.instantiateWasm callback failed with error: "+d),ba(d)}Pa(c,function(d){a(d.instance)}).catch(ba);return{}})();
var dc=b._emscripten_bind_ParagraphJustification___destroy___0=function(){return(dc=b._emscripten_bind_ParagraphJustification___destroy___0=b.asm.W).apply(null,arguments)},ec=b._emscripten_bind_BoolPtr___destroy___0=function(){return(ec=b._emscripten_bind_BoolPtr___destroy___0=b.asm.X).apply(null,arguments)},fc=b._emscripten_bind_TessResultRenderer_BeginDocument_1=function(){return(fc=b._emscripten_bind_TessResultRenderer_BeginDocument_1=b.asm.Y).apply(null,arguments)},gc=b._emscripten_bind_TessResultRenderer_AddImage_1=
function(){return(gc=b._emscripten_bind_TessResultRenderer_AddImage_1=b.asm.Z).apply(null,arguments)},hc=b._emscripten_bind_TessResultRenderer_EndDocument_0=function(){return(hc=b._emscripten_bind_TessResultRenderer_EndDocument_0=b.asm._).apply(null,arguments)},ic=b._emscripten_bind_TessResultRenderer_happy_0=function(){return(ic=b._emscripten_bind_TessResultRenderer_happy_0=b.asm.$).apply(null,arguments)},jc=b._emscripten_bind_TessResultRenderer_file_extension_0=function(){return(jc=b._emscripten_bind_TessResultRenderer_file_extension_0=
b.asm.aa).apply(null,arguments)},kc=b._emscripten_bind_TessResultRenderer_title_0=function(){return(kc=b._emscripten_bind_TessResultRenderer_title_0=b.asm.ba).apply(null,arguments)},lc=b._emscripten_bind_TessResultRenderer_imagenum_0=function(){return(lc=b._emscripten_bind_TessResultRenderer_imagenum_0=b.asm.ca).apply(null,arguments)},mc=b._emscripten_bind_TessResultRenderer___destroy___0=function(){return(mc=b._emscripten_bind_TessResultRenderer___destroy___0=b.asm.da).apply(null,arguments)},nc=
b._emscripten_bind_LongStarPtr___destroy___0=function(){return(nc=b._emscripten_bind_LongStarPtr___destroy___0=b.asm.ea).apply(null,arguments)},oc=b._emscripten_bind_VoidPtr___destroy___0=function(){return(oc=b._emscripten_bind_VoidPtr___destroy___0=b.asm.fa).apply(null,arguments)},pc=b._emscripten_bind_ResultIterator_ResultIterator_1=function(){return(pc=b._emscripten_bind_ResultIterator_ResultIterator_1=b.asm.ga).apply(null,arguments)},qc=b._emscripten_bind_ResultIterator_Begin_0=function(){return(qc=
b._emscripten_bind_ResultIterator_Begin_0=b.asm.ha).apply(null,arguments)},rc=b._emscripten_bind_ResultIterator_RestartParagraph_0=function(){return(rc=b._emscripten_bind_ResultIterator_RestartParagraph_0=b.asm.ia).apply(null,arguments)},sc=b._emscripten_bind_ResultIterator_IsWithinFirstTextlineOfParagraph_0=function(){return(sc=b._emscripten_bind_ResultIterator_IsWithinFirstTextlineOfParagraph_0=b.asm.ja).apply(null,arguments)},tc=b._emscripten_bind_ResultIterator_RestartRow_0=function(){return(tc=
b._emscripten_bind_ResultIterator_RestartRow_0=b.asm.ka).apply(null,arguments)},uc=b._emscripten_bind_ResultIterator_Next_1=function(){return(uc=b._emscripten_bind_ResultIterator_Next_1=b.asm.la).apply(null,arguments)},vc=b._emscripten_bind_ResultIterator_IsAtBeginningOf_1=function(){return(vc=b._emscripten_bind_ResultIterator_IsAtBeginningOf_1=b.asm.ma).apply(null,arguments)},wc=b._emscripten_bind_ResultIterator_IsAtFinalElement_2=function(){return(wc=b._emscripten_bind_ResultIterator_IsAtFinalElement_2=
b.asm.na).apply(null,arguments)},xc=b._emscripten_bind_ResultIterator_Cmp_1=function(){return(xc=b._emscripten_bind_ResultIterator_Cmp_1=b.asm.oa).apply(null,arguments)},yc=b._emscripten_bind_ResultIterator_SetBoundingBoxComponents_2=function(){return(yc=b._emscripten_bind_ResultIterator_SetBoundingBoxComponents_2=b.asm.pa).apply(null,arguments)},zc=b._emscripten_bind_ResultIterator_BoundingBox_5=function(){return(zc=b._emscripten_bind_ResultIterator_BoundingBox_5=b.asm.qa).apply(null,arguments)},
Ac=b._emscripten_bind_ResultIterator_BoundingBox_6=function(){return(Ac=b._emscripten_bind_ResultIterator_BoundingBox_6=b.asm.ra).apply(null,arguments)},Bc=b._emscripten_bind_ResultIterator_BoundingBoxInternal_5=function(){return(Bc=b._emscripten_bind_ResultIterator_BoundingBoxInternal_5=b.asm.sa).apply(null,arguments)},Cc=b._emscripten_bind_ResultIterator_Empty_1=function(){return(Cc=b._emscripten_bind_ResultIterator_Empty_1=b.asm.ta).apply(null,arguments)},Dc=b._emscripten_bind_ResultIterator_BlockType_0=
function(){return(Dc=b._emscripten_bind_ResultIterator_BlockType_0=b.asm.ua).apply(null,arguments)},Ec=b._emscripten_bind_ResultIterator_BlockPolygon_0=function(){return(Ec=b._emscripten_bind_ResultIterator_BlockPolygon_0=b.asm.va).apply(null,arguments)},Fc=b._emscripten_bind_ResultIterator_GetBinaryImage_1=function(){return(Fc=b._emscripten_bind_ResultIterator_GetBinaryImage_1=b.asm.wa).apply(null,arguments)},Gc=b._emscripten_bind_ResultIterator_GetImage_5=function(){return(Gc=b._emscripten_bind_ResultIterator_GetImage_5=
b.asm.xa).apply(null,arguments)},Hc=b._emscripten_bind_ResultIterator_Baseline_5=function(){return(Hc=b._emscripten_bind_ResultIterator_Baseline_5=b.asm.ya).apply(null,arguments)},Ic=b._emscripten_bind_ResultIterator_RowAttributes_3=function(){return(Ic=b._emscripten_bind_ResultIterator_RowAttributes_3=b.asm.za).apply(null,arguments)},Jc=b._emscripten_bind_ResultIterator_Orientation_4=function(){return(Jc=b._emscripten_bind_ResultIterator_Orientation_4=b.asm.Aa).apply(null,arguments)},Kc=b._emscripten_bind_ResultIterator_ParagraphInfo_4=
function(){return(Kc=b._emscripten_bind_ResultIterator_ParagraphInfo_4=b.asm.Ba).apply(null,arguments)},Lc=b._emscripten_bind_ResultIterator_ParagraphIsLtr_0=function(){return(Lc=b._emscripten_bind_ResultIterator_ParagraphIsLtr_0=b.asm.Ca).apply(null,arguments)},Mc=b._emscripten_bind_ResultIterator_GetUTF8Text_1=function(){return(Mc=b._emscripten_bind_ResultIterator_GetUTF8Text_1=b.asm.Da).apply(null,arguments)},Nc=b._emscripten_bind_ResultIterator_SetLineSeparator_1=function(){return(Nc=b._emscripten_bind_ResultIterator_SetLineSeparator_1=
b.asm.Ea).apply(null,arguments)},Oc=b._emscripten_bind_ResultIterator_SetParagraphSeparator_1=function(){return(Oc=b._emscripten_bind_ResultIterator_SetParagraphSeparator_1=b.asm.Fa).apply(null,arguments)},Pc=b._emscripten_bind_ResultIterator_Confidence_1=function(){return(Pc=b._emscripten_bind_ResultIterator_Confidence_1=b.asm.Ga).apply(null,arguments)},Qc=b._emscripten_bind_ResultIterator_WordFontAttributes_8=function(){return(Qc=b._emscripten_bind_ResultIterator_WordFontAttributes_8=b.asm.Ha).apply(null,
arguments)},Rc=b._emscripten_bind_ResultIterator_WordRecognitionLanguage_0=function(){return(Rc=b._emscripten_bind_ResultIterator_WordRecognitionLanguage_0=b.asm.Ia).apply(null,arguments)},Sc=b._emscripten_bind_ResultIterator_WordDirection_0=function(){return(Sc=b._emscripten_bind_ResultIterator_WordDirection_0=b.asm.Ja).apply(null,arguments)},Tc=b._emscripten_bind_ResultIterator_WordIsFromDictionary_0=function(){return(Tc=b._emscripten_bind_ResultIterator_WordIsFromDictionary_0=b.asm.Ka).apply(null,
arguments)},Uc=b._emscripten_bind_ResultIterator_WordIsNumeric_0=function(){return(Uc=b._emscripten_bind_ResultIterator_WordIsNumeric_0=b.asm.La).apply(null,arguments)},Vc=b._emscripten_bind_ResultIterator_HasBlamerInfo_0=function(){return(Vc=b._emscripten_bind_ResultIterator_HasBlamerInfo_0=b.asm.Ma).apply(null,arguments)},Wc=b._emscripten_bind_ResultIterator_HasTruthString_0=function(){return(Wc=b._emscripten_bind_ResultIterator_HasTruthString_0=b.asm.Na).apply(null,arguments)},Xc=b._emscripten_bind_ResultIterator_EquivalentToTruth_1=
function(){return(Xc=b._emscripten_bind_ResultIterator_EquivalentToTruth_1=b.asm.Oa).apply(null,arguments)},Yc=b._emscripten_bind_ResultIterator_WordTruthUTF8Text_0=function(){return(Yc=b._emscripten_bind_ResultIterator_WordTruthUTF8Text_0=b.asm.Pa).apply(null,arguments)},Zc=b._emscripten_bind_ResultIterator_WordNormedUTF8Text_0=function(){return(Zc=b._emscripten_bind_ResultIterator_WordNormedUTF8Text_0=b.asm.Qa).apply(null,arguments)},$c=b._emscripten_bind_ResultIterator_WordLattice_1=function(){return($c=
b._emscripten_bind_ResultIterator_WordLattice_1=b.asm.Ra).apply(null,arguments)},ad=b._emscripten_bind_ResultIterator_SymbolIsSuperscript_0=function(){return(ad=b._emscripten_bind_ResultIterator_SymbolIsSuperscript_0=b.asm.Sa).apply(null,arguments)},bd=b._emscripten_bind_ResultIterator_SymbolIsSubscript_0=function(){return(bd=b._emscripten_bind_ResultIterator_SymbolIsSubscript_0=b.asm.Ta).apply(null,arguments)},cd=b._emscripten_bind_ResultIterator_SymbolIsDropcap_0=function(){return(cd=b._emscripten_bind_ResultIterator_SymbolIsDropcap_0=
b.asm.Ua).apply(null,arguments)},dd=b._emscripten_bind_ResultIterator___destroy___0=function(){return(dd=b._emscripten_bind_ResultIterator___destroy___0=b.asm.Va).apply(null,arguments)},ed=b._emscripten_bind_TextlineOrder___destroy___0=function(){return(ed=b._emscripten_bind_TextlineOrder___destroy___0=b.asm.Wa).apply(null,arguments)},fd=b._emscripten_bind_ETEXT_DESC___destroy___0=function(){return(fd=b._emscripten_bind_ETEXT_DESC___destroy___0=b.asm.Xa).apply(null,arguments)},gd=b._emscripten_bind_PageIterator_Begin_0=
function(){return(gd=b._emscripten_bind_PageIterator_Begin_0=b.asm.Ya).apply(null,arguments)},hd=b._emscripten_bind_PageIterator_RestartParagraph_0=function(){return(hd=b._emscripten_bind_PageIterator_RestartParagraph_0=b.asm.Za).apply(null,arguments)},jd=b._emscripten_bind_PageIterator_IsWithinFirstTextlineOfParagraph_0=function(){return(jd=b._emscripten_bind_PageIterator_IsWithinFirstTextlineOfParagraph_0=b.asm._a).apply(null,arguments)},kd=b._emscripten_bind_PageIterator_RestartRow_0=function(){return(kd=
b._emscripten_bind_PageIterator_RestartRow_0=b.asm.$a).apply(null,arguments)},ld=b._emscripten_bind_PageIterator_Next_1=function(){return(ld=b._emscripten_bind_PageIterator_Next_1=b.asm.ab).apply(null,arguments)},md=b._emscripten_bind_PageIterator_IsAtBeginningOf_1=function(){return(md=b._emscripten_bind_PageIterator_IsAtBeginningOf_1=b.asm.bb).apply(null,arguments)},nd=b._emscripten_bind_PageIterator_IsAtFinalElement_2=function(){return(nd=b._emscripten_bind_PageIterator_IsAtFinalElement_2=b.asm.cb).apply(null,
arguments)},od=b._emscripten_bind_PageIterator_Cmp_1=function(){return(od=b._emscripten_bind_PageIterator_Cmp_1=b.asm.db).apply(null,arguments)},pd=b._emscripten_bind_PageIterator_SetBoundingBoxComponents_2=function(){return(pd=b._emscripten_bind_PageIterator_SetBoundingBoxComponents_2=b.asm.eb).apply(null,arguments)},qd=b._emscripten_bind_PageIterator_BoundingBox_5=function(){return(qd=b._emscripten_bind_PageIterator_BoundingBox_5=b.asm.fb).apply(null,arguments)},rd=b._emscripten_bind_PageIterator_BoundingBox_6=
function(){return(rd=b._emscripten_bind_PageIterator_BoundingBox_6=b.asm.gb).apply(null,arguments)},sd=b._emscripten_bind_PageIterator_BoundingBoxInternal_5=function(){return(sd=b._emscripten_bind_PageIterator_BoundingBoxInternal_5=b.asm.hb).apply(null,arguments)},td=b._emscripten_bind_PageIterator_Empty_1=function(){return(td=b._emscripten_bind_PageIterator_Empty_1=b.asm.ib).apply(null,arguments)},ud=b._emscripten_bind_PageIterator_BlockType_0=function(){return(ud=b._emscripten_bind_PageIterator_BlockType_0=
b.asm.jb).apply(null,arguments)},vd=b._emscripten_bind_PageIterator_BlockPolygon_0=function(){return(vd=b._emscripten_bind_PageIterator_BlockPolygon_0=b.asm.kb).apply(null,arguments)},wd=b._emscripten_bind_PageIterator_GetBinaryImage_1=function(){return(wd=b._emscripten_bind_PageIterator_GetBinaryImage_1=b.asm.lb).apply(null,arguments)},xd=b._emscripten_bind_PageIterator_GetImage_5=function(){return(xd=b._emscripten_bind_PageIterator_GetImage_5=b.asm.mb).apply(null,arguments)},yd=b._emscripten_bind_PageIterator_Baseline_5=
function(){return(yd=b._emscripten_bind_PageIterator_Baseline_5=b.asm.nb).apply(null,arguments)},zd=b._emscripten_bind_PageIterator_Orientation_4=function(){return(zd=b._emscripten_bind_PageIterator_Orientation_4=b.asm.ob).apply(null,arguments)},Ad=b._emscripten_bind_PageIterator_ParagraphInfo_4=function(){return(Ad=b._emscripten_bind_PageIterator_ParagraphInfo_4=b.asm.pb).apply(null,arguments)},Bd=b._emscripten_bind_PageIterator___destroy___0=function(){return(Bd=b._emscripten_bind_PageIterator___destroy___0=
b.asm.qb).apply(null,arguments)},Cd=b._emscripten_bind_WritingDirection___destroy___0=function(){return(Cd=b._emscripten_bind_WritingDirection___destroy___0=b.asm.rb).apply(null,arguments)},Dd=b._emscripten_bind_WordChoiceIterator_WordChoiceIterator_1=function(){return(Dd=b._emscripten_bind_WordChoiceIterator_WordChoiceIterator_1=b.asm.sb).apply(null,arguments)},Ed=b._emscripten_bind_WordChoiceIterator_Next_0=function(){return(Ed=b._emscripten_bind_WordChoiceIterator_Next_0=b.asm.tb).apply(null,arguments)},
Fd=b._emscripten_bind_WordChoiceIterator_GetUTF8Text_0=function(){return(Fd=b._emscripten_bind_WordChoiceIterator_GetUTF8Text_0=b.asm.ub).apply(null,arguments)},Gd=b._emscripten_bind_WordChoiceIterator_Confidence_0=function(){return(Gd=b._emscripten_bind_WordChoiceIterator_Confidence_0=b.asm.vb).apply(null,arguments)},Hd=b._emscripten_bind_WordChoiceIterator___destroy___0=function(){return(Hd=b._emscripten_bind_WordChoiceIterator___destroy___0=b.asm.wb).apply(null,arguments)},Id=b._emscripten_bind_Box_get_x_0=
function(){return(Id=b._emscripten_bind_Box_get_x_0=b.asm.xb).apply(null,arguments)},Jd=b._emscripten_bind_Box_get_y_0=function(){return(Jd=b._emscripten_bind_Box_get_y_0=b.asm.yb).apply(null,arguments)},Kd=b._emscripten_bind_Box_get_w_0=function(){return(Kd=b._emscripten_bind_Box_get_w_0=b.asm.zb).apply(null,arguments)},Ld=b._emscripten_bind_Box_get_h_0=function(){return(Ld=b._emscripten_bind_Box_get_h_0=b.asm.Ab).apply(null,arguments)},Md=b._emscripten_bind_Box_get_refcount_0=function(){return(Md=
b._emscripten_bind_Box_get_refcount_0=b.asm.Bb).apply(null,arguments)},Nd=b._emscripten_bind_Box___destroy___0=function(){return(Nd=b._emscripten_bind_Box___destroy___0=b.asm.Cb).apply(null,arguments)},Od=b._emscripten_bind_TessPDFRenderer_TessPDFRenderer_3=function(){return(Od=b._emscripten_bind_TessPDFRenderer_TessPDFRenderer_3=b.asm.Db).apply(null,arguments)},Pd=b._emscripten_bind_TessPDFRenderer_BeginDocument_1=function(){return(Pd=b._emscripten_bind_TessPDFRenderer_BeginDocument_1=b.asm.Eb).apply(null,
arguments)},Qd=b._emscripten_bind_TessPDFRenderer_AddImage_1=function(){return(Qd=b._emscripten_bind_TessPDFRenderer_AddImage_1=b.asm.Fb).apply(null,arguments)},Rd=b._emscripten_bind_TessPDFRenderer_EndDocument_0=function(){return(Rd=b._emscripten_bind_TessPDFRenderer_EndDocument_0=b.asm.Gb).apply(null,arguments)},Sd=b._emscripten_bind_TessPDFRenderer_happy_0=function(){return(Sd=b._emscripten_bind_TessPDFRenderer_happy_0=b.asm.Hb).apply(null,arguments)},Td=b._emscripten_bind_TessPDFRenderer_file_extension_0=
function(){return(Td=b._emscripten_bind_TessPDFRenderer_file_extension_0=b.asm.Ib).apply(null,arguments)},Ud=b._emscripten_bind_TessPDFRenderer_title_0=function(){return(Ud=b._emscripten_bind_TessPDFRenderer_title_0=b.asm.Jb).apply(null,arguments)},Vd=b._emscripten_bind_TessPDFRenderer_imagenum_0=function(){return(Vd=b._emscripten_bind_TessPDFRenderer_imagenum_0=b.asm.Kb).apply(null,arguments)},Wd=b._emscripten_bind_TessPDFRenderer___destroy___0=function(){return(Wd=b._emscripten_bind_TessPDFRenderer___destroy___0=
b.asm.Lb).apply(null,arguments)},Xd=b._emscripten_bind_PixaPtr___destroy___0=function(){return(Xd=b._emscripten_bind_PixaPtr___destroy___0=b.asm.Mb).apply(null,arguments)},Yd=b._emscripten_bind_FloatPtr___destroy___0=function(){return(Yd=b._emscripten_bind_FloatPtr___destroy___0=b.asm.Nb).apply(null,arguments)},Zd=b._emscripten_bind_ChoiceIterator_ChoiceIterator_1=function(){return(Zd=b._emscripten_bind_ChoiceIterator_ChoiceIterator_1=b.asm.Ob).apply(null,arguments)},$d=b._emscripten_bind_ChoiceIterator_Next_0=
function(){return($d=b._emscripten_bind_ChoiceIterator_Next_0=b.asm.Pb).apply(null,arguments)},ae=b._emscripten_bind_ChoiceIterator_GetUTF8Text_0=function(){return(ae=b._emscripten_bind_ChoiceIterator_GetUTF8Text_0=b.asm.Qb).apply(null,arguments)},be=b._emscripten_bind_ChoiceIterator_Confidence_0=function(){return(be=b._emscripten_bind_ChoiceIterator_Confidence_0=b.asm.Rb).apply(null,arguments)},ce=b._emscripten_bind_ChoiceIterator___destroy___0=function(){return(ce=b._emscripten_bind_ChoiceIterator___destroy___0=
b.asm.Sb).apply(null,arguments)},de=b._emscripten_bind_PixPtr___destroy___0=function(){return(de=b._emscripten_bind_PixPtr___destroy___0=b.asm.Tb).apply(null,arguments)},ee=b._emscripten_bind_UNICHARSET_get_script_from_script_id_1=function(){return(ee=b._emscripten_bind_UNICHARSET_get_script_from_script_id_1=b.asm.Ub).apply(null,arguments)},fe=b._emscripten_bind_UNICHARSET_get_script_id_from_name_1=function(){return(fe=b._emscripten_bind_UNICHARSET_get_script_id_from_name_1=b.asm.Vb).apply(null,arguments)},
ge=b._emscripten_bind_UNICHARSET_get_script_table_size_0=function(){return(ge=b._emscripten_bind_UNICHARSET_get_script_table_size_0=b.asm.Wb).apply(null,arguments)},he=b._emscripten_bind_UNICHARSET___destroy___0=function(){return(he=b._emscripten_bind_UNICHARSET___destroy___0=b.asm.Xb).apply(null,arguments)},ie=b._emscripten_bind_IntPtr___destroy___0=function(){return(ie=b._emscripten_bind_IntPtr___destroy___0=b.asm.Yb).apply(null,arguments)},je=b._emscripten_bind_Orientation___destroy___0=function(){return(je=
b._emscripten_bind_Orientation___destroy___0=b.asm.Zb).apply(null,arguments)},ke=b._emscripten_bind_OSBestResult_get_orientation_id_0=function(){return(ke=b._emscripten_bind_OSBestResult_get_orientation_id_0=b.asm._b).apply(null,arguments)},le=b._emscripten_bind_OSBestResult_get_script_id_0=function(){return(le=b._emscripten_bind_OSBestResult_get_script_id_0=b.asm.$b).apply(null,arguments)},me=b._emscripten_bind_OSBestResult_get_sconfidence_0=function(){return(me=b._emscripten_bind_OSBestResult_get_sconfidence_0=
b.asm.ac).apply(null,arguments)},ne=b._emscripten_bind_OSBestResult_get_oconfidence_0=function(){return(ne=b._emscripten_bind_OSBestResult_get_oconfidence_0=b.asm.bc).apply(null,arguments)},oe=b._emscripten_bind_OSBestResult___destroy___0=function(){return(oe=b._emscripten_bind_OSBestResult___destroy___0=b.asm.cc).apply(null,arguments)},pe=b._emscripten_bind_Boxa_get_n_0=function(){return(pe=b._emscripten_bind_Boxa_get_n_0=b.asm.dc).apply(null,arguments)},qe=b._emscripten_bind_Boxa_get_nalloc_0=function(){return(qe=
b._emscripten_bind_Boxa_get_nalloc_0=b.asm.ec).apply(null,arguments)},re=b._emscripten_bind_Boxa_get_refcount_0=function(){return(re=b._emscripten_bind_Boxa_get_refcount_0=b.asm.fc).apply(null,arguments)},se=b._emscripten_bind_Boxa_get_box_0=function(){return(se=b._emscripten_bind_Boxa_get_box_0=b.asm.gc).apply(null,arguments)},te=b._emscripten_bind_Boxa___destroy___0=function(){return(te=b._emscripten_bind_Boxa___destroy___0=b.asm.hc).apply(null,arguments)},ue=b._emscripten_bind_PixColormap_get_array_0=
function(){return(ue=b._emscripten_bind_PixColormap_get_array_0=b.asm.ic).apply(null,arguments)},ve=b._emscripten_bind_PixColormap_get_depth_0=function(){return(ve=b._emscripten_bind_PixColormap_get_depth_0=b.asm.jc).apply(null,arguments)},we=b._emscripten_bind_PixColormap_get_nalloc_0=function(){return(we=b._emscripten_bind_PixColormap_get_nalloc_0=b.asm.kc).apply(null,arguments)},xe=b._emscripten_bind_PixColormap_get_n_0=function(){return(xe=b._emscripten_bind_PixColormap_get_n_0=b.asm.lc).apply(null,
arguments)},ye=b._emscripten_bind_PixColormap___destroy___0=function(){return(ye=b._emscripten_bind_PixColormap___destroy___0=b.asm.mc).apply(null,arguments)},ze=b._emscripten_bind_Pta_get_n_0=function(){return(ze=b._emscripten_bind_Pta_get_n_0=b.asm.nc).apply(null,arguments)},Ae=b._emscripten_bind_Pta_get_nalloc_0=function(){return(Ae=b._emscripten_bind_Pta_get_nalloc_0=b.asm.oc).apply(null,arguments)},Be=b._emscripten_bind_Pta_get_refcount_0=function(){return(Be=b._emscripten_bind_Pta_get_refcount_0=
b.asm.pc).apply(null,arguments)},Ce=b._emscripten_bind_Pta_get_x_0=function(){return(Ce=b._emscripten_bind_Pta_get_x_0=b.asm.qc).apply(null,arguments)},De=b._emscripten_bind_Pta_get_y_0=function(){return(De=b._emscripten_bind_Pta_get_y_0=b.asm.rc).apply(null,arguments)},Ee=b._emscripten_bind_Pta___destroy___0=function(){return(Ee=b._emscripten_bind_Pta___destroy___0=b.asm.sc).apply(null,arguments)},Fe=b._emscripten_bind_Pix_get_w_0=function(){return(Fe=b._emscripten_bind_Pix_get_w_0=b.asm.tc).apply(null,
arguments)},Ge=b._emscripten_bind_Pix_get_h_0=function(){return(Ge=b._emscripten_bind_Pix_get_h_0=b.asm.uc).apply(null,arguments)},He=b._emscripten_bind_Pix_get_d_0=function(){return(He=b._emscripten_bind_Pix_get_d_0=b.asm.vc).apply(null,arguments)},Ie=b._emscripten_bind_Pix_get_spp_0=function(){return(Ie=b._emscripten_bind_Pix_get_spp_0=b.asm.wc).apply(null,arguments)},Je=b._emscripten_bind_Pix_get_wpl_0=function(){return(Je=b._emscripten_bind_Pix_get_wpl_0=b.asm.xc).apply(null,arguments)},Ke=b._emscripten_bind_Pix_get_refcount_0=
function(){return(Ke=b._emscripten_bind_Pix_get_refcount_0=b.asm.yc).apply(null,arguments)},Le=b._emscripten_bind_Pix_get_xres_0=function(){return(Le=b._emscripten_bind_Pix_get_xres_0=b.asm.zc).apply(null,arguments)},Me=b._emscripten_bind_Pix_get_yres_0=function(){return(Me=b._emscripten_bind_Pix_get_yres_0=b.asm.Ac).apply(null,arguments)},Ne=b._emscripten_bind_Pix_get_informat_0=function(){return(Ne=b._emscripten_bind_Pix_get_informat_0=b.asm.Bc).apply(null,arguments)},Oe=b._emscripten_bind_Pix_get_special_0=
function(){return(Oe=b._emscripten_bind_Pix_get_special_0=b.asm.Cc).apply(null,arguments)},Pe=b._emscripten_bind_Pix_get_text_0=function(){return(Pe=b._emscripten_bind_Pix_get_text_0=b.asm.Dc).apply(null,arguments)},Qe=b._emscripten_bind_Pix_get_colormap_0=function(){return(Qe=b._emscripten_bind_Pix_get_colormap_0=b.asm.Ec).apply(null,arguments)},Re=b._emscripten_bind_Pix_get_data_0=function(){return(Re=b._emscripten_bind_Pix_get_data_0=b.asm.Fc).apply(null,arguments)},Se=b._emscripten_bind_Pix___destroy___0=
function(){return(Se=b._emscripten_bind_Pix___destroy___0=b.asm.Gc).apply(null,arguments)},Te=b._emscripten_bind_DoublePtr___destroy___0=function(){return(Te=b._emscripten_bind_DoublePtr___destroy___0=b.asm.Hc).apply(null,arguments)},Ue=b._emscripten_bind_Dawg___destroy___0=function(){return(Ue=b._emscripten_bind_Dawg___destroy___0=b.asm.Ic).apply(null,arguments)},Ve=b._emscripten_bind_BoxPtr___destroy___0=function(){return(Ve=b._emscripten_bind_BoxPtr___destroy___0=b.asm.Jc).apply(null,arguments)},
We=b._emscripten_bind_TessBaseAPI_TessBaseAPI_0=function(){return(We=b._emscripten_bind_TessBaseAPI_TessBaseAPI_0=b.asm.Kc).apply(null,arguments)},Xe=b._emscripten_bind_TessBaseAPI_Version_0=function(){return(Xe=b._emscripten_bind_TessBaseAPI_Version_0=b.asm.Lc).apply(null,arguments)},Ye=b._emscripten_bind_TessBaseAPI_SetInputName_1=function(){return(Ye=b._emscripten_bind_TessBaseAPI_SetInputName_1=b.asm.Mc).apply(null,arguments)},Ze=b._emscripten_bind_TessBaseAPI_GetInputName_0=function(){return(Ze=
b._emscripten_bind_TessBaseAPI_GetInputName_0=b.asm.Nc).apply(null,arguments)},$e=b._emscripten_bind_TessBaseAPI_SetInputImage_1=function(){return($e=b._emscripten_bind_TessBaseAPI_SetInputImage_1=b.asm.Oc).apply(null,arguments)},af=b._emscripten_bind_TessBaseAPI_GetInputImage_0=function(){return(af=b._emscripten_bind_TessBaseAPI_GetInputImage_0=b.asm.Pc).apply(null,arguments)},bf=b._emscripten_bind_TessBaseAPI_GetSourceYResolution_0=function(){return(bf=b._emscripten_bind_TessBaseAPI_GetSourceYResolution_0=
b.asm.Qc).apply(null,arguments)},cf=b._emscripten_bind_TessBaseAPI_GetDatapath_0=function(){return(cf=b._emscripten_bind_TessBaseAPI_GetDatapath_0=b.asm.Rc).apply(null,arguments)},df=b._emscripten_bind_TessBaseAPI_SetOutputName_1=function(){return(df=b._emscripten_bind_TessBaseAPI_SetOutputName_1=b.asm.Sc).apply(null,arguments)},ef=b._emscripten_bind_TessBaseAPI_SetVariable_2=function(){return(ef=b._emscripten_bind_TessBaseAPI_SetVariable_2=b.asm.Tc).apply(null,arguments)},ff=b._emscripten_bind_TessBaseAPI_SetDebugVariable_2=
function(){return(ff=b._emscripten_bind_TessBaseAPI_SetDebugVariable_2=b.asm.Uc).apply(null,arguments)},gf=b._emscripten_bind_TessBaseAPI_GetIntVariable_2=function(){return(gf=b._emscripten_bind_TessBaseAPI_GetIntVariable_2=b.asm.Vc).apply(null,arguments)},hf=b._emscripten_bind_TessBaseAPI_GetBoolVariable_2=function(){return(hf=b._emscripten_bind_TessBaseAPI_GetBoolVariable_2=b.asm.Wc).apply(null,arguments)},jf=b._emscripten_bind_TessBaseAPI_GetDoubleVariable_2=function(){return(jf=b._emscripten_bind_TessBaseAPI_GetDoubleVariable_2=
b.asm.Xc).apply(null,arguments)},kf=b._emscripten_bind_TessBaseAPI_GetStringVariable_1=function(){return(kf=b._emscripten_bind_TessBaseAPI_GetStringVariable_1=b.asm.Yc).apply(null,arguments)},lf=b._emscripten_bind_TessBaseAPI_SaveParameters_1=function(){return(lf=b._emscripten_bind_TessBaseAPI_SaveParameters_1=b.asm.Zc).apply(null,arguments)},mf=b._emscripten_bind_TessBaseAPI_RestoreParameters_1=function(){return(mf=b._emscripten_bind_TessBaseAPI_RestoreParameters_1=b.asm._c).apply(null,arguments)},
nf=b._emscripten_bind_TessBaseAPI_Init_2=function(){return(nf=b._emscripten_bind_TessBaseAPI_Init_2=b.asm.$c).apply(null,arguments)},of=b._emscripten_bind_TessBaseAPI_Init_3=function(){return(of=b._emscripten_bind_TessBaseAPI_Init_3=b.asm.ad).apply(null,arguments)},pf=b._emscripten_bind_TessBaseAPI_Init_4=function(){return(pf=b._emscripten_bind_TessBaseAPI_Init_4=b.asm.bd).apply(null,arguments)},qf=b._emscripten_bind_TessBaseAPI_GetInitLanguagesAsString_0=function(){return(qf=b._emscripten_bind_TessBaseAPI_GetInitLanguagesAsString_0=
b.asm.cd).apply(null,arguments)},rf=b._emscripten_bind_TessBaseAPI_InitForAnalysePage_0=function(){return(rf=b._emscripten_bind_TessBaseAPI_InitForAnalysePage_0=b.asm.dd).apply(null,arguments)},sf=b._emscripten_bind_TessBaseAPI_ReadConfigFile_1=function(){return(sf=b._emscripten_bind_TessBaseAPI_ReadConfigFile_1=b.asm.ed).apply(null,arguments)},tf=b._emscripten_bind_TessBaseAPI_ReadDebugConfigFile_1=function(){return(tf=b._emscripten_bind_TessBaseAPI_ReadDebugConfigFile_1=b.asm.fd).apply(null,arguments)},
uf=b._emscripten_bind_TessBaseAPI_SetPageSegMode_1=function(){return(uf=b._emscripten_bind_TessBaseAPI_SetPageSegMode_1=b.asm.gd).apply(null,arguments)},vf=b._emscripten_bind_TessBaseAPI_GetPageSegMode_0=function(){return(vf=b._emscripten_bind_TessBaseAPI_GetPageSegMode_0=b.asm.hd).apply(null,arguments)},wf=b._emscripten_bind_TessBaseAPI_TesseractRect_7=function(){return(wf=b._emscripten_bind_TessBaseAPI_TesseractRect_7=b.asm.id).apply(null,arguments)},xf=b._emscripten_bind_TessBaseAPI_ClearAdaptiveClassifier_0=
function(){return(xf=b._emscripten_bind_TessBaseAPI_ClearAdaptiveClassifier_0=b.asm.jd).apply(null,arguments)},yf=b._emscripten_bind_TessBaseAPI_SetImage_1=function(){return(yf=b._emscripten_bind_TessBaseAPI_SetImage_1=b.asm.kd).apply(null,arguments)},zf=b._emscripten_bind_TessBaseAPI_SetImage_5=function(){return(zf=b._emscripten_bind_TessBaseAPI_SetImage_5=b.asm.ld).apply(null,arguments)},Af=b._emscripten_bind_TessBaseAPI_SetImageFile_1=function(){return(Af=b._emscripten_bind_TessBaseAPI_SetImageFile_1=
b.asm.md).apply(null,arguments)},Bf=b._emscripten_bind_TessBaseAPI_SetSourceResolution_1=function(){return(Bf=b._emscripten_bind_TessBaseAPI_SetSourceResolution_1=b.asm.nd).apply(null,arguments)},Cf=b._emscripten_bind_TessBaseAPI_SetRectangle_4=function(){return(Cf=b._emscripten_bind_TessBaseAPI_SetRectangle_4=b.asm.od).apply(null,arguments)},Df=b._emscripten_bind_TessBaseAPI_GetThresholdedImage_0=function(){return(Df=b._emscripten_bind_TessBaseAPI_GetThresholdedImage_0=b.asm.pd).apply(null,arguments)},
Ef=b._emscripten_bind_TessBaseAPI_WriteImage_0=function(){return(Ef=b._emscripten_bind_TessBaseAPI_WriteImage_0=b.asm.qd).apply(null,arguments)},Ff=b._emscripten_bind_TessBaseAPI_FindLines_0=function(){return(Ff=b._emscripten_bind_TessBaseAPI_FindLines_0=b.asm.rd).apply(null,arguments)},Gf=b._emscripten_bind_TessBaseAPI_GetGradient_0=function(){return(Gf=b._emscripten_bind_TessBaseAPI_GetGradient_0=b.asm.sd).apply(null,arguments)},Hf=b._emscripten_bind_TessBaseAPI_GetRegions_1=function(){return(Hf=
b._emscripten_bind_TessBaseAPI_GetRegions_1=b.asm.td).apply(null,arguments)},If=b._emscripten_bind_TessBaseAPI_GetTextlines_2=function(){return(If=b._emscripten_bind_TessBaseAPI_GetTextlines_2=b.asm.ud).apply(null,arguments)},Jf=b._emscripten_bind_TessBaseAPI_GetTextlines_5=function(){return(Jf=b._emscripten_bind_TessBaseAPI_GetTextlines_5=b.asm.vd).apply(null,arguments)},Kf=b._emscripten_bind_TessBaseAPI_GetStrips_2=function(){return(Kf=b._emscripten_bind_TessBaseAPI_GetStrips_2=b.asm.wd).apply(null,
arguments)},Lf=b._emscripten_bind_TessBaseAPI_GetWords_1=function(){return(Lf=b._emscripten_bind_TessBaseAPI_GetWords_1=b.asm.xd).apply(null,arguments)},Mf=b._emscripten_bind_TessBaseAPI_GetConnectedComponents_1=function(){return(Mf=b._emscripten_bind_TessBaseAPI_GetConnectedComponents_1=b.asm.yd).apply(null,arguments)},Nf=b._emscripten_bind_TessBaseAPI_GetComponentImages_4=function(){return(Nf=b._emscripten_bind_TessBaseAPI_GetComponentImages_4=b.asm.zd).apply(null,arguments)},Of=b._emscripten_bind_TessBaseAPI_GetComponentImages_7=
function(){return(Of=b._emscripten_bind_TessBaseAPI_GetComponentImages_7=b.asm.Ad).apply(null,arguments)},Pf=b._emscripten_bind_TessBaseAPI_GetThresholdedImageScaleFactor_0=function(){return(Pf=b._emscripten_bind_TessBaseAPI_GetThresholdedImageScaleFactor_0=b.asm.Bd).apply(null,arguments)},Qf=b._emscripten_bind_TessBaseAPI_AnalyseLayout_0=function(){return(Qf=b._emscripten_bind_TessBaseAPI_AnalyseLayout_0=b.asm.Cd).apply(null,arguments)},Rf=b._emscripten_bind_TessBaseAPI_AnalyseLayout_1=function(){return(Rf=
b._emscripten_bind_TessBaseAPI_AnalyseLayout_1=b.asm.Dd).apply(null,arguments)},Sf=b._emscripten_bind_TessBaseAPI_Recognize_1=function(){return(Sf=b._emscripten_bind_TessBaseAPI_Recognize_1=b.asm.Ed).apply(null,arguments)},Tf=b._emscripten_bind_TessBaseAPI_ProcessPages_4=function(){return(Tf=b._emscripten_bind_TessBaseAPI_ProcessPages_4=b.asm.Fd).apply(null,arguments)},Uf=b._emscripten_bind_TessBaseAPI_ProcessPage_6=function(){return(Uf=b._emscripten_bind_TessBaseAPI_ProcessPage_6=b.asm.Gd).apply(null,
arguments)},Vf=b._emscripten_bind_TessBaseAPI_GetIterator_0=function(){return(Vf=b._emscripten_bind_TessBaseAPI_GetIterator_0=b.asm.Hd).apply(null,arguments)},Wf=b._emscripten_bind_TessBaseAPI_GetUTF8Text_0=function(){return(Wf=b._emscripten_bind_TessBaseAPI_GetUTF8Text_0=b.asm.Id).apply(null,arguments)},Xf=b._emscripten_bind_TessBaseAPI_GetHOCRText_1=function(){return(Xf=b._emscripten_bind_TessBaseAPI_GetHOCRText_1=b.asm.Jd).apply(null,arguments)},Yf=b._emscripten_bind_TessBaseAPI_GetJSONText_1=
function(){return(Yf=b._emscripten_bind_TessBaseAPI_GetJSONText_1=b.asm.Kd).apply(null,arguments)},Zf=b._emscripten_bind_TessBaseAPI_GetTSVText_1=function(){return(Zf=b._emscripten_bind_TessBaseAPI_GetTSVText_1=b.asm.Ld).apply(null,arguments)},$f=b._emscripten_bind_TessBaseAPI_GetBoxText_1=function(){return($f=b._emscripten_bind_TessBaseAPI_GetBoxText_1=b.asm.Md).apply(null,arguments)},ag=b._emscripten_bind_TessBaseAPI_GetUNLVText_0=function(){return(ag=b._emscripten_bind_TessBaseAPI_GetUNLVText_0=
b.asm.Nd).apply(null,arguments)},bg=b._emscripten_bind_TessBaseAPI_GetOsdText_1=function(){return(bg=b._emscripten_bind_TessBaseAPI_GetOsdText_1=b.asm.Od).apply(null,arguments)},cg=b._emscripten_bind_TessBaseAPI_MeanTextConf_0=function(){return(cg=b._emscripten_bind_TessBaseAPI_MeanTextConf_0=b.asm.Pd).apply(null,arguments)},dg=b._emscripten_bind_TessBaseAPI_AllWordConfidences_0=function(){return(dg=b._emscripten_bind_TessBaseAPI_AllWordConfidences_0=b.asm.Qd).apply(null,arguments)},eg=b._emscripten_bind_TessBaseAPI_Clear_0=
function(){return(eg=b._emscripten_bind_TessBaseAPI_Clear_0=b.asm.Rd).apply(null,arguments)},fg=b._emscripten_bind_TessBaseAPI_End_0=function(){return(fg=b._emscripten_bind_TessBaseAPI_End_0=b.asm.Sd).apply(null,arguments)},gg=b._emscripten_bind_TessBaseAPI_ClearPersistentCache_0=function(){return(gg=b._emscripten_bind_TessBaseAPI_ClearPersistentCache_0=b.asm.Td).apply(null,arguments)},hg=b._emscripten_bind_TessBaseAPI_IsValidWord_1=function(){return(hg=b._emscripten_bind_TessBaseAPI_IsValidWord_1=
b.asm.Ud).apply(null,arguments)},ig=b._emscripten_bind_TessBaseAPI_IsValidCharacter_1=function(){return(ig=b._emscripten_bind_TessBaseAPI_IsValidCharacter_1=b.asm.Vd).apply(null,arguments)},jg=b._emscripten_bind_TessBaseAPI_DetectOS_1=function(){return(jg=b._emscripten_bind_TessBaseAPI_DetectOS_1=b.asm.Wd).apply(null,arguments)},kg=b._emscripten_bind_TessBaseAPI_GetUnichar_1=function(){return(kg=b._emscripten_bind_TessBaseAPI_GetUnichar_1=b.asm.Xd).apply(null,arguments)},lg=b._emscripten_bind_TessBaseAPI_GetDawg_1=
function(){return(lg=b._emscripten_bind_TessBaseAPI_GetDawg_1=b.asm.Yd).apply(null,arguments)},mg=b._emscripten_bind_TessBaseAPI_NumDawgs_0=function(){return(mg=b._emscripten_bind_TessBaseAPI_NumDawgs_0=b.asm.Zd).apply(null,arguments)},ng=b._emscripten_bind_TessBaseAPI_oem_0=function(){return(ng=b._emscripten_bind_TessBaseAPI_oem_0=b.asm._d).apply(null,arguments)},og=b._emscripten_bind_TessBaseAPI___destroy___0=function(){return(og=b._emscripten_bind_TessBaseAPI___destroy___0=b.asm.$d).apply(null,
arguments)},pg=b._emscripten_bind_OSResults_OSResults_0=function(){return(pg=b._emscripten_bind_OSResults_OSResults_0=b.asm.ae).apply(null,arguments)},qg=b._emscripten_bind_OSResults_print_scores_0=function(){return(qg=b._emscripten_bind_OSResults_print_scores_0=b.asm.be).apply(null,arguments)},rg=b._emscripten_bind_OSResults_get_best_result_0=function(){return(rg=b._emscripten_bind_OSResults_get_best_result_0=b.asm.ce).apply(null,arguments)},sg=b._emscripten_bind_OSResults_get_unicharset_0=function(){return(sg=
b._emscripten_bind_OSResults_get_unicharset_0=b.asm.de).apply(null,arguments)},tg=b._emscripten_bind_OSResults___destroy___0=function(){return(tg=b._emscripten_bind_OSResults___destroy___0=b.asm.ee).apply(null,arguments)},ug=b._emscripten_bind_Pixa_get_n_0=function(){return(ug=b._emscripten_bind_Pixa_get_n_0=b.asm.fe).apply(null,arguments)},vg=b._emscripten_bind_Pixa_get_nalloc_0=function(){return(vg=b._emscripten_bind_Pixa_get_nalloc_0=b.asm.ge).apply(null,arguments)},wg=b._emscripten_bind_Pixa_get_refcount_0=
function(){return(wg=b._emscripten_bind_Pixa_get_refcount_0=b.asm.he).apply(null,arguments)},xg=b._emscripten_bind_Pixa_get_pix_0=function(){return(xg=b._emscripten_bind_Pixa_get_pix_0=b.asm.ie).apply(null,arguments)},yg=b._emscripten_bind_Pixa_get_boxa_0=function(){return(yg=b._emscripten_bind_Pixa_get_boxa_0=b.asm.je).apply(null,arguments)},zg=b._emscripten_bind_Pixa___destroy___0=function(){return(zg=b._emscripten_bind_Pixa___destroy___0=b.asm.ke).apply(null,arguments)},Ag=b._emscripten_enum_PageIteratorLevel_RIL_BLOCK=
function(){return(Ag=b._emscripten_enum_PageIteratorLevel_RIL_BLOCK=b.asm.le).apply(null,arguments)},Bg=b._emscripten_enum_PageIteratorLevel_RIL_PARA=function(){return(Bg=b._emscripten_enum_PageIteratorLevel_RIL_PARA=b.asm.me).apply(null,arguments)},Cg=b._emscripten_enum_PageIteratorLevel_RIL_TEXTLINE=function(){return(Cg=b._emscripten_enum_PageIteratorLevel_RIL_TEXTLINE=b.asm.ne).apply(null,arguments)},Dg=b._emscripten_enum_PageIteratorLevel_RIL_WORD=function(){return(Dg=b._emscripten_enum_PageIteratorLevel_RIL_WORD=
b.asm.oe).apply(null,arguments)},Eg=b._emscripten_enum_PageIteratorLevel_RIL_SYMBOL=function(){return(Eg=b._emscripten_enum_PageIteratorLevel_RIL_SYMBOL=b.asm.pe).apply(null,arguments)},Fg=b._emscripten_enum_OcrEngineMode_OEM_TESSERACT_ONLY=function(){return(Fg=b._emscripten_enum_OcrEngineMode_OEM_TESSERACT_ONLY=b.asm.qe).apply(null,arguments)},Gg=b._emscripten_enum_OcrEngineMode_OEM_LSTM_ONLY=function(){return(Gg=b._emscripten_enum_OcrEngineMode_OEM_LSTM_ONLY=b.asm.re).apply(null,arguments)},Hg=
b._emscripten_enum_OcrEngineMode_OEM_TESSERACT_LSTM_COMBINED=function(){return(Hg=b._emscripten_enum_OcrEngineMode_OEM_TESSERACT_LSTM_COMBINED=b.asm.se).apply(null,arguments)},Ig=b._emscripten_enum_OcrEngineMode_OEM_DEFAULT=function(){return(Ig=b._emscripten_enum_OcrEngineMode_OEM_DEFAULT=b.asm.te).apply(null,arguments)},Jg=b._emscripten_enum_OcrEngineMode_OEM_COUNT=function(){return(Jg=b._emscripten_enum_OcrEngineMode_OEM_COUNT=b.asm.ue).apply(null,arguments)},Kg=b._emscripten_enum_WritingDirection__WRITING_DIRECTION_LEFT_TO_RIGHT=
function(){return(Kg=b._emscripten_enum_WritingDirection__WRITING_DIRECTION_LEFT_TO_RIGHT=b.asm.ve).apply(null,arguments)},Lg=b._emscripten_enum_WritingDirection__WRITING_DIRECTION_RIGHT_TO_LEFT=function(){return(Lg=b._emscripten_enum_WritingDirection__WRITING_DIRECTION_RIGHT_TO_LEFT=b.asm.we).apply(null,arguments)},Mg=b._emscripten_enum_WritingDirection__WRITING_DIRECTION_TOP_TO_BOTTOM=function(){return(Mg=b._emscripten_enum_WritingDirection__WRITING_DIRECTION_TOP_TO_BOTTOM=b.asm.xe).apply(null,
arguments)},Ng=b._emscripten_enum_PolyBlockType_PT_UNKNOWN=function(){return(Ng=b._emscripten_enum_PolyBlockType_PT_UNKNOWN=b.asm.ye).apply(null,arguments)},Og=b._emscripten_enum_PolyBlockType_PT_FLOWING_TEXT=function(){return(Og=b._emscripten_enum_PolyBlockType_PT_FLOWING_TEXT=b.asm.ze).apply(null,arguments)},Pg=b._emscripten_enum_PolyBlockType_PT_HEADING_TEXT=function(){return(Pg=b._emscripten_enum_PolyBlockType_PT_HEADING_TEXT=b.asm.Ae).apply(null,arguments)},Qg=b._emscripten_enum_PolyBlockType_PT_PULLOUT_TEXT=
function(){return(Qg=b._emscripten_enum_PolyBlockType_PT_PULLOUT_TEXT=b.asm.Be).apply(null,arguments)},Rg=b._emscripten_enum_PolyBlockType_PT_EQUATION=function(){return(Rg=b._emscripten_enum_PolyBlockType_PT_EQUATION=b.asm.Ce).apply(null,arguments)},Sg=b._emscripten_enum_PolyBlockType_PT_INLINE_EQUATION=function(){return(Sg=b._emscripten_enum_PolyBlockType_PT_INLINE_EQUATION=b.asm.De).apply(null,arguments)},Tg=b._emscripten_enum_PolyBlockType_PT_TABLE=function(){return(Tg=b._emscripten_enum_PolyBlockType_PT_TABLE=
b.asm.Ee).apply(null,arguments)},Ug=b._emscripten_enum_PolyBlockType_PT_VERTICAL_TEXT=function(){return(Ug=b._emscripten_enum_PolyBlockType_PT_VERTICAL_TEXT=b.asm.Fe).apply(null,arguments)},Vg=b._emscripten_enum_PolyBlockType_PT_CAPTION_TEXT=function(){return(Vg=b._emscripten_enum_PolyBlockType_PT_CAPTION_TEXT=b.asm.Ge).apply(null,arguments)},Wg=b._emscripten_enum_PolyBlockType_PT_FLOWING_IMAGE=function(){return(Wg=b._emscripten_enum_PolyBlockType_PT_FLOWING_IMAGE=b.asm.He).apply(null,arguments)},
Xg=b._emscripten_enum_PolyBlockType_PT_HEADING_IMAGE=function(){return(Xg=b._emscripten_enum_PolyBlockType_PT_HEADING_IMAGE=b.asm.Ie).apply(null,arguments)},Yg=b._emscripten_enum_PolyBlockType_PT_PULLOUT_IMAGE=function(){return(Yg=b._emscripten_enum_PolyBlockType_PT_PULLOUT_IMAGE=b.asm.Je).apply(null,arguments)},Zg=b._emscripten_enum_PolyBlockType_PT_HORZ_LINE=function(){return(Zg=b._emscripten_enum_PolyBlockType_PT_HORZ_LINE=b.asm.Ke).apply(null,arguments)},$g=b._emscripten_enum_PolyBlockType_PT_VERT_LINE=
function(){return($g=b._emscripten_enum_PolyBlockType_PT_VERT_LINE=b.asm.Le).apply(null,arguments)},ah=b._emscripten_enum_PolyBlockType_PT_NOISE=function(){return(ah=b._emscripten_enum_PolyBlockType_PT_NOISE=b.asm.Me).apply(null,arguments)},bh=b._emscripten_enum_PolyBlockType_PT_COUNT=function(){return(bh=b._emscripten_enum_PolyBlockType_PT_COUNT=b.asm.Ne).apply(null,arguments)},ch=b._emscripten_enum_StrongScriptDirection_DIR_NEUTRAL=function(){return(ch=b._emscripten_enum_StrongScriptDirection_DIR_NEUTRAL=
b.asm.Oe).apply(null,arguments)},dh=b._emscripten_enum_StrongScriptDirection_DIR_LEFT_TO_RIGHT=function(){return(dh=b._emscripten_enum_StrongScriptDirection_DIR_LEFT_TO_RIGHT=b.asm.Pe).apply(null,arguments)},eh=b._emscripten_enum_StrongScriptDirection_DIR_RIGHT_TO_LEFT=function(){return(eh=b._emscripten_enum_StrongScriptDirection_DIR_RIGHT_TO_LEFT=b.asm.Qe).apply(null,arguments)},fh=b._emscripten_enum_StrongScriptDirection_DIR_MIX=function(){return(fh=b._emscripten_enum_StrongScriptDirection_DIR_MIX=
b.asm.Re).apply(null,arguments)},gh=b._emscripten_enum_ParagraphJustification__JUSTIFICATION_UNKNOWN=function(){return(gh=b._emscripten_enum_ParagraphJustification__JUSTIFICATION_UNKNOWN=b.asm.Se).apply(null,arguments)},hh=b._emscripten_enum_ParagraphJustification__JUSTIFICATION_LEFT=function(){return(hh=b._emscripten_enum_ParagraphJustification__JUSTIFICATION_LEFT=b.asm.Te).apply(null,arguments)},ih=b._emscripten_enum_ParagraphJustification__JUSTIFICATION_CENTER=function(){return(ih=b._emscripten_enum_ParagraphJustification__JUSTIFICATION_CENTER=
b.asm.Ue).apply(null,arguments)},jh=b._emscripten_enum_ParagraphJustification__JUSTIFICATION_RIGHT=function(){return(jh=b._emscripten_enum_ParagraphJustification__JUSTIFICATION_RIGHT=b.asm.Ve).apply(null,arguments)},kh=b._emscripten_enum_TextlineOrder__TEXTLINE_ORDER_LEFT_TO_RIGHT=function(){return(kh=b._emscripten_enum_TextlineOrder__TEXTLINE_ORDER_LEFT_TO_RIGHT=b.asm.We).apply(null,arguments)},lh=b._emscripten_enum_TextlineOrder__TEXTLINE_ORDER_RIGHT_TO_LEFT=function(){return(lh=b._emscripten_enum_TextlineOrder__TEXTLINE_ORDER_RIGHT_TO_LEFT=
b.asm.Xe).apply(null,arguments)},mh=b._emscripten_enum_TextlineOrder__TEXTLINE_ORDER_TOP_TO_BOTTOM=function(){return(mh=b._emscripten_enum_TextlineOrder__TEXTLINE_ORDER_TOP_TO_BOTTOM=b.asm.Ye).apply(null,arguments)},nh=b._emscripten_enum_Orientation__ORIENTATION_PAGE_UP=function(){return(nh=b._emscripten_enum_Orientation__ORIENTATION_PAGE_UP=b.asm.Ze).apply(null,arguments)},oh=b._emscripten_enum_Orientation__ORIENTATION_PAGE_RIGHT=function(){return(oh=b._emscripten_enum_Orientation__ORIENTATION_PAGE_RIGHT=
b.asm._e).apply(null,arguments)},ph=b._emscripten_enum_Orientation__ORIENTATION_PAGE_DOWN=function(){return(ph=b._emscripten_enum_Orientation__ORIENTATION_PAGE_DOWN=b.asm.$e).apply(null,arguments)},qh=b._emscripten_enum_Orientation__ORIENTATION_PAGE_LEFT=function(){return(qh=b._emscripten_enum_Orientation__ORIENTATION_PAGE_LEFT=b.asm.af).apply(null,arguments)},rh=b._emscripten_enum_PageSegMode_PSM_OSD_ONLY=function(){return(rh=b._emscripten_enum_PageSegMode_PSM_OSD_ONLY=b.asm.bf).apply(null,arguments)},
sh=b._emscripten_enum_PageSegMode_PSM_AUTO_OSD=function(){return(sh=b._emscripten_enum_PageSegMode_PSM_AUTO_OSD=b.asm.cf).apply(null,arguments)},th=b._emscripten_enum_PageSegMode_PSM_AUTO_ONLY=function(){return(th=b._emscripten_enum_PageSegMode_PSM_AUTO_ONLY=b.asm.df).apply(null,arguments)},uh=b._emscripten_enum_PageSegMode_PSM_AUTO=function(){return(uh=b._emscripten_enum_PageSegMode_PSM_AUTO=b.asm.ef).apply(null,arguments)},vh=b._emscripten_enum_PageSegMode_PSM_SINGLE_COLUMN=function(){return(vh=
b._emscripten_enum_PageSegMode_PSM_SINGLE_COLUMN=b.asm.ff).apply(null,arguments)},wh=b._emscripten_enum_PageSegMode_PSM_SINGLE_BLOCK_VERT_TEXT=function(){return(wh=b._emscripten_enum_PageSegMode_PSM_SINGLE_BLOCK_VERT_TEXT=b.asm.gf).apply(null,arguments)},xh=b._emscripten_enum_PageSegMode_PSM_SINGLE_BLOCK=function(){return(xh=b._emscripten_enum_PageSegMode_PSM_SINGLE_BLOCK=b.asm.hf).apply(null,arguments)},yh=b._emscripten_enum_PageSegMode_PSM_SINGLE_LINE=function(){return(yh=b._emscripten_enum_PageSegMode_PSM_SINGLE_LINE=
b.asm.jf).apply(null,arguments)},zh=b._emscripten_enum_PageSegMode_PSM_SINGLE_WORD=function(){return(zh=b._emscripten_enum_PageSegMode_PSM_SINGLE_WORD=b.asm.kf).apply(null,arguments)},Ah=b._emscripten_enum_PageSegMode_PSM_CIRCLE_WORD=function(){return(Ah=b._emscripten_enum_PageSegMode_PSM_CIRCLE_WORD=b.asm.lf).apply(null,arguments)},Bh=b._emscripten_enum_PageSegMode_PSM_SINGLE_CHAR=function(){return(Bh=b._emscripten_enum_PageSegMode_PSM_SINGLE_CHAR=b.asm.mf).apply(null,arguments)},Ch=b._emscripten_enum_PageSegMode_PSM_SPARSE_TEXT=
function(){return(Ch=b._emscripten_enum_PageSegMode_PSM_SPARSE_TEXT=b.asm.nf).apply(null,arguments)},Dh=b._emscripten_enum_PageSegMode_PSM_SPARSE_TEXT_OSD=function(){return(Dh=b._emscripten_enum_PageSegMode_PSM_SPARSE_TEXT_OSD=b.asm.of).apply(null,arguments)},Eh=b._emscripten_enum_PageSegMode_PSM_RAW_LINE=function(){return(Eh=b._emscripten_enum_PageSegMode_PSM_RAW_LINE=b.asm.pf).apply(null,arguments)},Fh=b._emscripten_enum_PageSegMode_PSM_COUNT=function(){return(Fh=b._emscripten_enum_PageSegMode_PSM_COUNT=
b.asm.qf).apply(null,arguments)};b._pixDestroy=function(){return(b._pixDestroy=b.asm.sf).apply(null,arguments)};b._ptaDestroy=function(){return(b._ptaDestroy=b.asm.tf).apply(null,arguments)};b._pixaDestroy=function(){return(b._pixaDestroy=b.asm.uf).apply(null,arguments)};b._boxaDestroy=function(){return(b._boxaDestroy=b.asm.vf).apply(null,arguments)};b._pixReadMem=function(){return(b._pixReadMem=b.asm.wf).apply(null,arguments)};function Rb(){return(Rb=b.asm.xf).apply(null,arguments)}
var Gh=b._free=function(){return(Gh=b._free=b.asm.yf).apply(null,arguments)},Fb=b._malloc=function(){return(Fb=b._malloc=b.asm.zf).apply(null,arguments)};b._pixReadHeaderMem=function(){return(b._pixReadHeaderMem=b.asm.Af).apply(null,arguments)};function C(){return(C=b.asm.Bf).apply(null,arguments)}function D(){return(D=b.asm.Cf).apply(null,arguments)}b.___emscripten_embedded_file_data=600448;function Ub(a,c,d,e){var g=C();try{return Pb(a)(c,d,e)}catch(h){D(g);if(h!==h+0)throw h;zb()}}
function Xb(a,c){var d=C();try{Pb(a)(c)}catch(e){D(d);if(e!==e+0)throw e;zb()}}function Sb(a,c){var d=C();try{return Pb(a)(c)}catch(e){D(d);if(e!==e+0)throw e;zb()}}function Zb(a,c,d,e){var g=C();try{Pb(a)(c,d,e)}catch(h){D(g);if(h!==h+0)throw h;zb()}}function Yb(a,c,d){var e=C();try{Pb(a)(c,d)}catch(g){D(e);if(g!==g+0)throw g;zb()}}function Tb(a,c,d){var e=C();try{return Pb(a)(c,d)}catch(g){D(e);if(g!==g+0)throw g;zb()}}
function Vb(a,c,d,e,g){var h=C();try{return Pb(a)(c,d,e,g)}catch(k){D(h);if(k!==k+0)throw k;zb()}}function $b(a,c,d,e,g){var h=C();try{Pb(a)(c,d,e,g)}catch(k){D(h);if(k!==k+0)throw k;zb()}}function Wb(a,c,d,e,g,h){var k=C();try{return Pb(a)(c,d,e,g,h)}catch(m){D(k);if(m!==m+0)throw m;zb()}}function bc(a,c,d,e,g,h,k,m,v,q){var t=C();try{Pb(a)(c,d,e,g,h,k,m,v,q)}catch(F){D(t);if(F!==F+0)throw F;zb()}}function ac(a,c,d,e,g,h){var k=C();try{Pb(a)(c,d,e,g,h)}catch(m){D(k);if(m!==m+0)throw m;zb()}}
b.addRunDependency=Ha;b.removeRunDependency=Ia;b.FS_createPath=A.Eg;b.FS_createDataFile=A.vg;b.FS_createLazyFile=A.bh;b.FS_createDevice=A.Rf;b.FS_unlink=A.unlink;b.setValue=Ya;b.getValue=Xa;b.FS_createPreloadedFile=A.dh;b.FS=A;var Hh;Ga=function Ih(){Hh||Jh();Hh||(Ga=Ih)};
function Jh(){function a(){if(!Hh&&(Hh=!0,b.calledRun=!0,!sa)){Ca=!0;b.noFSInit||A.dg.Pg||A.dg();A.ph=!1;Sa(Aa);aa(b);if(b.onRuntimeInitialized)b.onRuntimeInitialized();if(b.postRun)for("function"==typeof b.postRun&&(b.postRun=[b.postRun]);b.postRun.length;){var c=b.postRun.shift();Ba.unshift(c)}Sa(Ba)}}if(!(0<Ea)){if(b.preRun)for("function"==typeof b.preRun&&(b.preRun=[b.preRun]);b.preRun.length;)Da();Sa(za);0<Ea||(b.setStatus?(b.setStatus("Running..."),setTimeout(function(){setTimeout(function(){b.setStatus("")},
1);a()},1)):a())}}if(b.preInit)for("function"==typeof b.preInit&&(b.preInit=[b.preInit]);0<b.preInit.length;)b.preInit.pop()();Jh();function G(){}G.prototype=Object.create(G.prototype);G.prototype.constructor=G;G.prototype.If=G;G.Jf={};b.WrapperObject=G;function Kh(a){return(a||G).Jf}b.getCache=Kh;function H(a,c){var d=Kh(c),e=d[a];if(e)return e;e=Object.create((c||G).prototype);e.Df=a;return d[a]=e}b.wrapPointer=H;b.castObject=function(a,c){return H(a.Df,c)};b.NULL=H(0);
b.destroy=function(a){if(!a.__destroy__)throw"Error: Cannot destroy object. (Did you create it yourself?)";a.__destroy__();delete Kh(a.If)[a.Df]};b.compare=function(a,c){return a.Df===c.Df};b.getPointer=function(a){return a.Df};b.getClass=function(a){return a.If};var Lh=0,Mh=0,Nh=0,Oh=[],Ph=0;function I(){if(Ph){for(var a=0;a<Oh.length;a++)b._free(Oh[a]);Oh.length=0;b._free(Lh);Lh=0;Mh+=Ph;Ph=0}Lh||(Mh+=128,(Lh=b._malloc(Mh))||n());Nh=0}
function J(a){if("string"===typeof a){a=kb(a);var c=p;Lh||n();c=a.length*c.BYTES_PER_ELEMENT;c=c+7&-8;if(Nh+c>=Mh){0<c||n();Ph+=c;var d=b._malloc(c);Oh.push(d)}else d=Lh+Nh,Nh+=c;c=d;d=p;var e=c;switch(d.BYTES_PER_ELEMENT){case 2:e>>=1;break;case 4:e>>=2;break;case 8:e>>=3}for(var g=0;g<a.length;g++)d[e+g]=a[g];return c}return a}function Qh(){throw"cannot construct a ParagraphJustification, no constructor in IDL";}Qh.prototype=Object.create(G.prototype);Qh.prototype.constructor=Qh;
Qh.prototype.If=Qh;Qh.Jf={};b.ParagraphJustification=Qh;Qh.prototype.__destroy__=function(){dc(this.Df)};function Rh(){throw"cannot construct a BoolPtr, no constructor in IDL";}Rh.prototype=Object.create(G.prototype);Rh.prototype.constructor=Rh;Rh.prototype.If=Rh;Rh.Jf={};b.BoolPtr=Rh;Rh.prototype.__destroy__=function(){ec(this.Df)};function K(){throw"cannot construct a TessResultRenderer, no constructor in IDL";}K.prototype=Object.create(G.prototype);K.prototype.constructor=K;K.prototype.If=K;
K.Jf={};b.TessResultRenderer=K;K.prototype.BeginDocument=function(a){var c=this.Df;I();a=a&&"object"===typeof a?a.Df:J(a);return!!fc(c,a)};K.prototype.AddImage=function(a){var c=this.Df;a&&"object"===typeof a&&(a=a.Df);return!!gc(c,a)};K.prototype.EndDocument=function(){return!!hc(this.Df)};K.prototype.happy=function(){return!!ic(this.Df)};K.prototype.file_extension=function(){return z(jc(this.Df))};K.prototype.title=K.prototype.title=function(){return z(kc(this.Df))};K.prototype.imagenum=function(){return lc(this.Df)};
K.prototype.__destroy__=function(){mc(this.Df)};function Sh(){throw"cannot construct a LongStarPtr, no constructor in IDL";}Sh.prototype=Object.create(G.prototype);Sh.prototype.constructor=Sh;Sh.prototype.If=Sh;Sh.Jf={};b.LongStarPtr=Sh;Sh.prototype.__destroy__=function(){nc(this.Df)};function Th(){throw"cannot construct a VoidPtr, no constructor in IDL";}Th.prototype=Object.create(G.prototype);Th.prototype.constructor=Th;Th.prototype.If=Th;Th.Jf={};b.VoidPtr=Th;Th.prototype.__destroy__=function(){oc(this.Df)};
function L(a){a&&"object"===typeof a&&(a=a.Df);this.Df=pc(a);Kh(L)[this.Df]=this}L.prototype=Object.create(G.prototype);L.prototype.constructor=L;L.prototype.If=L;L.Jf={};b.ResultIterator=L;L.prototype.Begin=function(){qc(this.Df)};L.prototype.RestartParagraph=function(){rc(this.Df)};L.prototype.IsWithinFirstTextlineOfParagraph=function(){return!!sc(this.Df)};L.prototype.RestartRow=function(){tc(this.Df)};L.prototype.Next=function(a){var c=this.Df;a&&"object"===typeof a&&(a=a.Df);return!!uc(c,a)};
L.prototype.IsAtBeginningOf=function(a){var c=this.Df;a&&"object"===typeof a&&(a=a.Df);return!!vc(c,a)};L.prototype.IsAtFinalElement=function(a,c){var d=this.Df;a&&"object"===typeof a&&(a=a.Df);c&&"object"===typeof c&&(c=c.Df);return!!wc(d,a,c)};L.prototype.Cmp=function(a){var c=this.Df;a&&"object"===typeof a&&(a=a.Df);return xc(c,a)};L.prototype.SetBoundingBoxComponents=function(a,c){var d=this.Df;a&&"object"===typeof a&&(a=a.Df);c&&"object"===typeof c&&(c=c.Df);yc(d,a,c)};
L.prototype.BoundingBox=function(a,c,d,e,g,h){var k=this.Df;a&&"object"===typeof a&&(a=a.Df);c&&"object"===typeof c&&(c=c.Df);d&&"object"===typeof d&&(d=d.Df);e&&"object"===typeof e&&(e=e.Df);g&&"object"===typeof g&&(g=g.Df);h&&"object"===typeof h&&(h=h.Df);return void 0===h?!!zc(k,a,c,d,e,g):!!Ac(k,a,c,d,e,g,h)};
L.prototype.BoundingBoxInternal=function(a,c,d,e,g){var h=this.Df;a&&"object"===typeof a&&(a=a.Df);c&&"object"===typeof c&&(c=c.Df);d&&"object"===typeof d&&(d=d.Df);e&&"object"===typeof e&&(e=e.Df);g&&"object"===typeof g&&(g=g.Df);return!!Bc(h,a,c,d,e,g)};L.prototype.Empty=function(a){var c=this.Df;a&&"object"===typeof a&&(a=a.Df);return!!Cc(c,a)};L.prototype.BlockType=function(){return Dc(this.Df)};L.prototype.BlockPolygon=function(){return H(Ec(this.Df),M)};
L.prototype.GetBinaryImage=function(a){var c=this.Df;a&&"object"===typeof a&&(a=a.Df);return H(Fc(c,a),N)};L.prototype.GetImage=function(a,c,d,e,g){var h=this.Df;a&&"object"===typeof a&&(a=a.Df);c&&"object"===typeof c&&(c=c.Df);d&&"object"===typeof d&&(d=d.Df);e&&"object"===typeof e&&(e=e.Df);g&&"object"===typeof g&&(g=g.Df);return H(Gc(h,a,c,d,e,g),N)};
L.prototype.Baseline=function(a,c,d,e,g){var h=this.Df;a&&"object"===typeof a&&(a=a.Df);c&&"object"===typeof c&&(c=c.Df);d&&"object"===typeof d&&(d=d.Df);e&&"object"===typeof e&&(e=e.Df);g&&"object"===typeof g&&(g=g.Df);return!!Hc(h,a,c,d,e,g)};L.prototype.RowAttributes=function(a,c,d){var e=this.Df;a&&"object"===typeof a&&(a=a.Df);c&&"object"===typeof c&&(c=c.Df);d&&"object"===typeof d&&(d=d.Df);return!!Ic(e,a,c,d)};
L.prototype.Orientation=function(a,c,d,e){var g=this.Df;a&&"object"===typeof a&&(a=a.Df);c&&"object"===typeof c&&(c=c.Df);d&&"object"===typeof d&&(d=d.Df);e&&"object"===typeof e&&(e=e.Df);Jc(g,a,c,d,e)};L.prototype.ParagraphInfo=function(a,c,d,e){var g=this.Df;a&&"object"===typeof a&&(a=a.Df);c&&"object"===typeof c&&(c=c.Df);d&&"object"===typeof d&&(d=d.Df);e&&"object"===typeof e&&(e=e.Df);Kc(g,a,c,d,e)};L.prototype.ParagraphIsLtr=function(){return!!Lc(this.Df)};
L.prototype.GetUTF8Text=function(a){var c=this.Df;a&&"object"===typeof a&&(a=a.Df);return z(Mc(c,a))};L.prototype.SetLineSeparator=function(a){var c=this.Df;I();a=a&&"object"===typeof a?a.Df:J(a);Nc(c,a)};L.prototype.SetParagraphSeparator=function(a){var c=this.Df;I();a=a&&"object"===typeof a?a.Df:J(a);Oc(c,a)};L.prototype.Confidence=function(a){var c=this.Df;a&&"object"===typeof a&&(a=a.Df);return Pc(c,a)};
L.prototype.WordFontAttributes=function(a,c,d,e,g,h,k,m){var v=this.Df;a&&"object"===typeof a&&(a=a.Df);c&&"object"===typeof c&&(c=c.Df);d&&"object"===typeof d&&(d=d.Df);e&&"object"===typeof e&&(e=e.Df);g&&"object"===typeof g&&(g=g.Df);h&&"object"===typeof h&&(h=h.Df);k&&"object"===typeof k&&(k=k.Df);m&&"object"===typeof m&&(m=m.Df);return z(Qc(v,a,c,d,e,g,h,k,m))};L.prototype.WordRecognitionLanguage=function(){return z(Rc(this.Df))};L.prototype.WordDirection=function(){return Sc(this.Df)};
L.prototype.WordIsFromDictionary=function(){return!!Tc(this.Df)};L.prototype.WordIsNumeric=function(){return!!Uc(this.Df)};L.prototype.HasBlamerInfo=function(){return!!Vc(this.Df)};L.prototype.HasTruthString=function(){return!!Wc(this.Df)};L.prototype.EquivalentToTruth=function(a){var c=this.Df;I();a=a&&"object"===typeof a?a.Df:J(a);return!!Xc(c,a)};L.prototype.WordTruthUTF8Text=function(){return z(Yc(this.Df))};L.prototype.WordNormedUTF8Text=function(){return z(Zc(this.Df))};
L.prototype.WordLattice=function(a){var c=this.Df;a&&"object"===typeof a&&(a=a.Df);return z($c(c,a))};L.prototype.SymbolIsSuperscript=function(){return!!ad(this.Df)};L.prototype.SymbolIsSubscript=function(){return!!bd(this.Df)};L.prototype.SymbolIsDropcap=function(){return!!cd(this.Df)};L.prototype.__destroy__=function(){dd(this.Df)};function Vh(){throw"cannot construct a TextlineOrder, no constructor in IDL";}Vh.prototype=Object.create(G.prototype);Vh.prototype.constructor=Vh;Vh.prototype.If=Vh;
Vh.Jf={};b.TextlineOrder=Vh;Vh.prototype.__destroy__=function(){ed(this.Df)};function Wh(){throw"cannot construct a ETEXT_DESC, no constructor in IDL";}Wh.prototype=Object.create(G.prototype);Wh.prototype.constructor=Wh;Wh.prototype.If=Wh;Wh.Jf={};b.ETEXT_DESC=Wh;Wh.prototype.__destroy__=function(){fd(this.Df)};function O(){throw"cannot construct a PageIterator, no constructor in IDL";}O.prototype=Object.create(G.prototype);O.prototype.constructor=O;O.prototype.If=O;O.Jf={};b.PageIterator=O;
O.prototype.Begin=function(){gd(this.Df)};O.prototype.RestartParagraph=function(){hd(this.Df)};O.prototype.IsWithinFirstTextlineOfParagraph=function(){return!!jd(this.Df)};O.prototype.RestartRow=function(){kd(this.Df)};O.prototype.Next=function(a){var c=this.Df;a&&"object"===typeof a&&(a=a.Df);return!!ld(c,a)};O.prototype.IsAtBeginningOf=function(a){var c=this.Df;a&&"object"===typeof a&&(a=a.Df);return!!md(c,a)};
O.prototype.IsAtFinalElement=function(a,c){var d=this.Df;a&&"object"===typeof a&&(a=a.Df);c&&"object"===typeof c&&(c=c.Df);return!!nd(d,a,c)};O.prototype.Cmp=function(a){var c=this.Df;a&&"object"===typeof a&&(a=a.Df);return od(c,a)};O.prototype.SetBoundingBoxComponents=function(a,c){var d=this.Df;a&&"object"===typeof a&&(a=a.Df);c&&"object"===typeof c&&(c=c.Df);pd(d,a,c)};
O.prototype.BoundingBox=function(a,c,d,e,g,h){var k=this.Df;a&&"object"===typeof a&&(a=a.Df);c&&"object"===typeof c&&(c=c.Df);d&&"object"===typeof d&&(d=d.Df);e&&"object"===typeof e&&(e=e.Df);g&&"object"===typeof g&&(g=g.Df);h&&"object"===typeof h&&(h=h.Df);return void 0===h?!!qd(k,a,c,d,e,g):!!rd(k,a,c,d,e,g,h)};
O.prototype.BoundingBoxInternal=function(a,c,d,e,g){var h=this.Df;a&&"object"===typeof a&&(a=a.Df);c&&"object"===typeof c&&(c=c.Df);d&&"object"===typeof d&&(d=d.Df);e&&"object"===typeof e&&(e=e.Df);g&&"object"===typeof g&&(g=g.Df);return!!sd(h,a,c,d,e,g)};O.prototype.Empty=function(a){var c=this.Df;a&&"object"===typeof a&&(a=a.Df);return!!td(c,a)};O.prototype.BlockType=function(){return ud(this.Df)};O.prototype.BlockPolygon=function(){return H(vd(this.Df),M)};
O.prototype.GetBinaryImage=function(a){var c=this.Df;a&&"object"===typeof a&&(a=a.Df);return H(wd(c,a),N)};O.prototype.GetImage=function(a,c,d,e,g){var h=this.Df;a&&"object"===typeof a&&(a=a.Df);c&&"object"===typeof c&&(c=c.Df);d&&"object"===typeof d&&(d=d.Df);e&&"object"===typeof e&&(e=e.Df);g&&"object"===typeof g&&(g=g.Df);return H(xd(h,a,c,d,e,g),N)};
O.prototype.Baseline=function(a,c,d,e,g){var h=this.Df;a&&"object"===typeof a&&(a=a.Df);c&&"object"===typeof c&&(c=c.Df);d&&"object"===typeof d&&(d=d.Df);e&&"object"===typeof e&&(e=e.Df);g&&"object"===typeof g&&(g=g.Df);return!!yd(h,a,c,d,e,g)};O.prototype.Orientation=function(a,c,d,e){var g=this.Df;a&&"object"===typeof a&&(a=a.Df);c&&"object"===typeof c&&(c=c.Df);d&&"object"===typeof d&&(d=d.Df);e&&"object"===typeof e&&(e=e.Df);zd(g,a,c,d,e)};
O.prototype.ParagraphInfo=function(a,c,d,e){var g=this.Df;a&&"object"===typeof a&&(a=a.Df);c&&"object"===typeof c&&(c=c.Df);d&&"object"===typeof d&&(d=d.Df);e&&"object"===typeof e&&(e=e.Df);Ad(g,a,c,d,e)};O.prototype.__destroy__=function(){Bd(this.Df)};function Xh(){throw"cannot construct a WritingDirection, no constructor in IDL";}Xh.prototype=Object.create(G.prototype);Xh.prototype.constructor=Xh;Xh.prototype.If=Xh;Xh.Jf={};b.WritingDirection=Xh;Xh.prototype.__destroy__=function(){Cd(this.Df)};
function Yh(a){a&&"object"===typeof a&&(a=a.Df);this.Df=Dd(a);Kh(Yh)[this.Df]=this}Yh.prototype=Object.create(G.prototype);Yh.prototype.constructor=Yh;Yh.prototype.If=Yh;Yh.Jf={};b.WordChoiceIterator=Yh;Yh.prototype.Next=function(){return!!Ed(this.Df)};Yh.prototype.GetUTF8Text=function(){return z(Fd(this.Df))};Yh.prototype.Confidence=function(){return Gd(this.Df)};Yh.prototype.__destroy__=function(){Hd(this.Df)};function P(){throw"cannot construct a Box, no constructor in IDL";}P.prototype=Object.create(G.prototype);
P.prototype.constructor=P;P.prototype.If=P;P.Jf={};b.Box=P;P.prototype.get_x=P.prototype.Lg=function(){return Id(this.Df)};Object.defineProperty(P.prototype,"x",{get:P.prototype.Lg});P.prototype.get_y=P.prototype.Mg=function(){return Jd(this.Df)};Object.defineProperty(P.prototype,"y",{get:P.prototype.Mg});P.prototype.get_w=P.prototype.Kg=function(){return Kd(this.Df)};Object.defineProperty(P.prototype,"w",{get:P.prototype.Kg});P.prototype.get_h=P.prototype.Jg=function(){return Ld(this.Df)};
Object.defineProperty(P.prototype,"h",{get:P.prototype.Jg});P.prototype.get_refcount=P.prototype.Yf=function(){return Md(this.Df)};Object.defineProperty(P.prototype,"refcount",{get:P.prototype.Yf});P.prototype.__destroy__=function(){Nd(this.Df)};function Q(a,c,d){I();a=a&&"object"===typeof a?a.Df:J(a);c=c&&"object"===typeof c?c.Df:J(c);d&&"object"===typeof d&&(d=d.Df);this.Df=Od(a,c,d);Kh(Q)[this.Df]=this}Q.prototype=Object.create(G.prototype);Q.prototype.constructor=Q;Q.prototype.If=Q;Q.Jf={};
b.TessPDFRenderer=Q;Q.prototype.BeginDocument=function(a){var c=this.Df;I();a=a&&"object"===typeof a?a.Df:J(a);return!!Pd(c,a)};Q.prototype.AddImage=function(a){var c=this.Df;a&&"object"===typeof a&&(a=a.Df);return!!Qd(c,a)};Q.prototype.EndDocument=function(){return!!Rd(this.Df)};Q.prototype.happy=function(){return!!Sd(this.Df)};Q.prototype.file_extension=function(){return z(Td(this.Df))};Q.prototype.title=Q.prototype.title=function(){return z(Ud(this.Df))};Q.prototype.imagenum=function(){return Vd(this.Df)};
Q.prototype.__destroy__=function(){Wd(this.Df)};function Zh(){throw"cannot construct a PixaPtr, no constructor in IDL";}Zh.prototype=Object.create(G.prototype);Zh.prototype.constructor=Zh;Zh.prototype.If=Zh;Zh.Jf={};b.PixaPtr=Zh;Zh.prototype.__destroy__=function(){Xd(this.Df)};function $h(){throw"cannot construct a FloatPtr, no constructor in IDL";}$h.prototype=Object.create(G.prototype);$h.prototype.constructor=$h;$h.prototype.If=$h;$h.Jf={};b.FloatPtr=$h;$h.prototype.__destroy__=function(){Yd(this.Df)};
function ai(a){a&&"object"===typeof a&&(a=a.Df);this.Df=Zd(a);Kh(ai)[this.Df]=this}ai.prototype=Object.create(G.prototype);ai.prototype.constructor=ai;ai.prototype.If=ai;ai.Jf={};b.ChoiceIterator=ai;ai.prototype.Next=function(){return!!$d(this.Df)};ai.prototype.GetUTF8Text=function(){return z(ae(this.Df))};ai.prototype.Confidence=function(){return be(this.Df)};ai.prototype.__destroy__=function(){ce(this.Df)};function bi(){throw"cannot construct a PixPtr, no constructor in IDL";}bi.prototype=Object.create(G.prototype);
bi.prototype.constructor=bi;bi.prototype.If=bi;bi.Jf={};b.PixPtr=bi;bi.prototype.__destroy__=function(){de(this.Df)};function ci(){throw"cannot construct a UNICHARSET, no constructor in IDL";}ci.prototype=Object.create(G.prototype);ci.prototype.constructor=ci;ci.prototype.If=ci;ci.Jf={};b.UNICHARSET=ci;ci.prototype.get_script_from_script_id=function(a){var c=this.Df;a&&"object"===typeof a&&(a=a.Df);return z(ee(c,a))};
ci.prototype.get_script_id_from_name=function(a){var c=this.Df;I();a=a&&"object"===typeof a?a.Df:J(a);return fe(c,a)};ci.prototype.get_script_table_size=function(){return ge(this.Df)};ci.prototype.__destroy__=function(){he(this.Df)};function di(){throw"cannot construct a IntPtr, no constructor in IDL";}di.prototype=Object.create(G.prototype);di.prototype.constructor=di;di.prototype.If=di;di.Jf={};b.IntPtr=di;di.prototype.__destroy__=function(){ie(this.Df)};
function ei(){throw"cannot construct a Orientation, no constructor in IDL";}ei.prototype=Object.create(G.prototype);ei.prototype.constructor=ei;ei.prototype.If=ei;ei.Jf={};b.Orientation=ei;ei.prototype.__destroy__=function(){je(this.Df)};function R(){throw"cannot construct a OSBestResult, no constructor in IDL";}R.prototype=Object.create(G.prototype);R.prototype.constructor=R;R.prototype.If=R;R.Jf={};b.OSBestResult=R;R.prototype.get_orientation_id=R.prototype.Vh=function(){return ke(this.Df)};
Object.defineProperty(R.prototype,"orientation_id",{get:R.prototype.Vh});R.prototype.get_script_id=R.prototype.Yh=function(){return le(this.Df)};Object.defineProperty(R.prototype,"script_id",{get:R.prototype.Yh});R.prototype.get_sconfidence=R.prototype.Xh=function(){return me(this.Df)};Object.defineProperty(R.prototype,"sconfidence",{get:R.prototype.Xh});R.prototype.get_oconfidence=R.prototype.Uh=function(){return ne(this.Df)};Object.defineProperty(R.prototype,"oconfidence",{get:R.prototype.Uh});
R.prototype.__destroy__=function(){oe(this.Df)};function S(){throw"cannot construct a Boxa, no constructor in IDL";}S.prototype=Object.create(G.prototype);S.prototype.constructor=S;S.prototype.If=S;S.Jf={};b.Boxa=S;S.prototype.get_n=S.prototype.bg=function(){return pe(this.Df)};Object.defineProperty(S.prototype,"n",{get:S.prototype.bg});S.prototype.get_nalloc=S.prototype.cg=function(){return qe(this.Df)};Object.defineProperty(S.prototype,"nalloc",{get:S.prototype.cg});
S.prototype.get_refcount=S.prototype.Yf=function(){return re(this.Df)};Object.defineProperty(S.prototype,"refcount",{get:S.prototype.Yf});S.prototype.get_box=S.prototype.Nh=function(){return H(se(this.Df),fi)};Object.defineProperty(S.prototype,"box",{get:S.prototype.Nh});S.prototype.__destroy__=function(){te(this.Df)};function T(){throw"cannot construct a PixColormap, no constructor in IDL";}T.prototype=Object.create(G.prototype);T.prototype.constructor=T;T.prototype.If=T;T.Jf={};b.PixColormap=T;
T.prototype.get_array=T.prototype.Lh=function(){return ue(this.Df)};Object.defineProperty(T.prototype,"array",{get:T.prototype.Lh});T.prototype.get_depth=T.prototype.Sh=function(){return ve(this.Df)};Object.defineProperty(T.prototype,"depth",{get:T.prototype.Sh});T.prototype.get_nalloc=T.prototype.cg=function(){return we(this.Df)};Object.defineProperty(T.prototype,"nalloc",{get:T.prototype.cg});T.prototype.get_n=T.prototype.bg=function(){return xe(this.Df)};Object.defineProperty(T.prototype,"n",{get:T.prototype.bg});
T.prototype.__destroy__=function(){ye(this.Df)};function M(){throw"cannot construct a Pta, no constructor in IDL";}M.prototype=Object.create(G.prototype);M.prototype.constructor=M;M.prototype.If=M;M.Jf={};b.Pta=M;M.prototype.get_n=M.prototype.bg=function(){return ze(this.Df)};Object.defineProperty(M.prototype,"n",{get:M.prototype.bg});M.prototype.get_nalloc=M.prototype.cg=function(){return Ae(this.Df)};Object.defineProperty(M.prototype,"nalloc",{get:M.prototype.cg});
M.prototype.get_refcount=M.prototype.Yf=function(){return Be(this.Df)};Object.defineProperty(M.prototype,"refcount",{get:M.prototype.Yf});M.prototype.get_x=M.prototype.Lg=function(){return H(Ce(this.Df),$h)};Object.defineProperty(M.prototype,"x",{get:M.prototype.Lg});M.prototype.get_y=M.prototype.Mg=function(){return H(De(this.Df),$h)};Object.defineProperty(M.prototype,"y",{get:M.prototype.Mg});M.prototype.__destroy__=function(){Ee(this.Df)};
function N(){throw"cannot construct a Pix, no constructor in IDL";}N.prototype=Object.create(G.prototype);N.prototype.constructor=N;N.prototype.If=N;N.Jf={};b.Pix=N;N.prototype.get_w=N.prototype.Kg=function(){return Fe(this.Df)};Object.defineProperty(N.prototype,"w",{get:N.prototype.Kg});N.prototype.get_h=N.prototype.Jg=function(){return Ge(this.Df)};Object.defineProperty(N.prototype,"h",{get:N.prototype.Jg});N.prototype.get_d=N.prototype.Qh=function(){return He(this.Df)};
Object.defineProperty(N.prototype,"d",{get:N.prototype.Qh});N.prototype.get_spp=N.prototype.$h=function(){return Ie(this.Df)};Object.defineProperty(N.prototype,"spp",{get:N.prototype.$h});N.prototype.get_wpl=N.prototype.ci=function(){return Je(this.Df)};Object.defineProperty(N.prototype,"wpl",{get:N.prototype.ci});N.prototype.get_refcount=N.prototype.Yf=function(){return Ke(this.Df)};Object.defineProperty(N.prototype,"refcount",{get:N.prototype.Yf});N.prototype.get_xres=N.prototype.di=function(){return Le(this.Df)};
Object.defineProperty(N.prototype,"xres",{get:N.prototype.di});N.prototype.get_yres=N.prototype.ei=function(){return Me(this.Df)};Object.defineProperty(N.prototype,"yres",{get:N.prototype.ei});N.prototype.get_informat=N.prototype.Th=function(){return Ne(this.Df)};Object.defineProperty(N.prototype,"informat",{get:N.prototype.Th});N.prototype.get_special=N.prototype.Zh=function(){return Oe(this.Df)};Object.defineProperty(N.prototype,"special",{get:N.prototype.Zh});
N.prototype.get_text=N.prototype.ai=function(){return z(Pe(this.Df))};Object.defineProperty(N.prototype,"text",{get:N.prototype.ai});N.prototype.get_colormap=N.prototype.Ph=function(){return H(Qe(this.Df),T)};Object.defineProperty(N.prototype,"colormap",{get:N.prototype.Ph});N.prototype.get_data=N.prototype.Rh=function(){return Re(this.Df)};Object.defineProperty(N.prototype,"data",{get:N.prototype.Rh});N.prototype.__destroy__=function(){Se(this.Df)};
function gi(){throw"cannot construct a DoublePtr, no constructor in IDL";}gi.prototype=Object.create(G.prototype);gi.prototype.constructor=gi;gi.prototype.If=gi;gi.Jf={};b.DoublePtr=gi;gi.prototype.__destroy__=function(){Te(this.Df)};function hi(){throw"cannot construct a Dawg, no constructor in IDL";}hi.prototype=Object.create(G.prototype);hi.prototype.constructor=hi;hi.prototype.If=hi;hi.Jf={};b.Dawg=hi;hi.prototype.__destroy__=function(){Ue(this.Df)};
function fi(){throw"cannot construct a BoxPtr, no constructor in IDL";}fi.prototype=Object.create(G.prototype);fi.prototype.constructor=fi;fi.prototype.If=fi;fi.Jf={};b.BoxPtr=fi;fi.prototype.__destroy__=function(){Ve(this.Df)};function X(){this.Df=We();Kh(X)[this.Df]=this}X.prototype=Object.create(G.prototype);X.prototype.constructor=X;X.prototype.If=X;X.Jf={};b.TessBaseAPI=X;X.prototype.Version=function(){return z(Xe(this.Df))};
X.prototype.SetInputName=function(a){var c=this.Df;I();a=a&&"object"===typeof a?a.Df:J(a);Ye(c,a)};X.prototype.GetInputName=function(){return z(Ze(this.Df))};X.prototype.SetInputImage=function(a){var c=this.Df;a&&"object"===typeof a&&(a=a.Df);$e(c,a)};X.prototype.GetInputImage=function(){return H(af(this.Df),N)};X.prototype.GetSourceYResolution=function(){return bf(this.Df)};X.prototype.GetDatapath=function(){return z(cf(this.Df))};
X.prototype.SetOutputName=function(a){var c=this.Df;I();a=a&&"object"===typeof a?a.Df:J(a);df(c,a)};X.prototype.SetVariable=X.prototype.SetVariable=function(a,c){var d=this.Df;I();a=a&&"object"===typeof a?a.Df:J(a);c=c&&"object"===typeof c?c.Df:J(c);return!!ef(d,a,c)};X.prototype.SetDebugVariable=function(a,c){var d=this.Df;I();a=a&&"object"===typeof a?a.Df:J(a);c=c&&"object"===typeof c?c.Df:J(c);return!!ff(d,a,c)};
X.prototype.GetIntVariable=function(a,c){var d=this.Df;I();a=a&&"object"===typeof a?a.Df:J(a);c&&"object"===typeof c&&(c=c.Df);return!!gf(d,a,c)};X.prototype.GetBoolVariable=function(a,c){var d=this.Df;I();a=a&&"object"===typeof a?a.Df:J(a);c&&"object"===typeof c&&(c=c.Df);return!!hf(d,a,c)};X.prototype.GetDoubleVariable=function(a,c){var d=this.Df;I();a=a&&"object"===typeof a?a.Df:J(a);c&&"object"===typeof c&&(c=c.Df);return!!jf(d,a,c)};
X.prototype.GetStringVariable=function(a){var c=this.Df;I();a=a&&"object"===typeof a?a.Df:J(a);return z(kf(c,a))};X.prototype.Init=function(a,c,d,e){void 0===d&&void 0!==e&&(d=3);var g=this.Df;I();a=a&&"object"===typeof a?a.Df:J(a);c=c&&"object"===typeof c?c.Df:J(c);e=e&&"object"===typeof e?e.Df:J(e);d&&"object"===typeof d&&(d=d.Df);return void 0===d&&void 0!==e?pf(g,a,c,3,e):void 0===d?nf(g,a,c):void 0===e?of(g,a,c,d):pf(g,a,c,d,e)};X.prototype.GetInitLanguagesAsString=function(){return z(qf(this.Df))};
X.prototype.InitForAnalysePage=function(){rf(this.Df)};X.prototype.SaveParameters=function(){lf(this.Df)};X.prototype.RestoreParameters=function(){mf(this.Df)};X.prototype.ReadConfigFile=function(a){var c=this.Df;I();a=a&&"object"===typeof a?a.Df:J(a);sf(c,a)};X.prototype.ReadDebugConfigFile=function(a){var c=this.Df;I();a=a&&"object"===typeof a?a.Df:J(a);tf(c,a)};X.prototype.SetPageSegMode=function(a){var c=this.Df;a&&"object"===typeof a&&(a=a.Df);uf(c,a)};X.prototype.GetPageSegMode=function(){return vf(this.Df)};
X.prototype.TesseractRect=function(a,c,d,e,g,h,k){var m=this.Df;a&&"object"===typeof a&&(a=a.Df);c&&"object"===typeof c&&(c=c.Df);d&&"object"===typeof d&&(d=d.Df);e&&"object"===typeof e&&(e=e.Df);g&&"object"===typeof g&&(g=g.Df);h&&"object"===typeof h&&(h=h.Df);k&&"object"===typeof k&&(k=k.Df);return z(wf(m,a,c,d,e,g,h,k))};X.prototype.ClearAdaptiveClassifier=function(){xf(this.Df)};
X.prototype.SetImage=function(a,c,d,e,g,h=1,k=0){var m=this.Df;a&&"object"===typeof a&&(a=a.Df);c&&"object"===typeof c&&(c=c.Df);d&&"object"===typeof d&&(d=d.Df);e&&"object"===typeof e&&(e=e.Df);g&&"object"===typeof g&&(g=g.Df);void 0===c||null===c?yf(m,a,h,k):zf(m,a,c,d,e,g,h,k)};X.prototype.SetImageFile=function(a=1,c=0){return Af(this.Df,a,c)};X.prototype.SetSourceResolution=function(a){var c=this.Df;a&&"object"===typeof a&&(a=a.Df);Bf(c,a)};
X.prototype.SetRectangle=function(a,c,d,e){var g=this.Df;a&&"object"===typeof a&&(a=a.Df);c&&"object"===typeof c&&(c=c.Df);d&&"object"===typeof d&&(d=d.Df);e&&"object"===typeof e&&(e=e.Df);Cf(g,a,c,d,e)};X.prototype.GetThresholdedImage=function(){return H(Df(this.Df),N)};X.prototype.WriteImage=function(a){Ef(this.Df,a)};X.prototype.GetRegions=function(a){var c=this.Df;a&&"object"===typeof a&&(a=a.Df);return H(Hf(c,a),S)};
X.prototype.GetTextlines=function(a,c,d,e,g){var h=this.Df;a&&"object"===typeof a&&(a=a.Df);c&&"object"===typeof c&&(c=c.Df);d&&"object"===typeof d&&(d=d.Df);e&&"object"===typeof e&&(e=e.Df);g&&"object"===typeof g&&(g=g.Df);return void 0===d?H(If(h,a,c),S):void 0===e?H(_emscripten_bind_TessBaseAPI_GetTextlines_3(h,a,c,d),S):void 0===g?H(_emscripten_bind_TessBaseAPI_GetTextlines_4(h,a,c,d,e),S):H(Jf(h,a,c,d,e,g),S)};
X.prototype.GetStrips=function(a,c){var d=this.Df;a&&"object"===typeof a&&(a=a.Df);c&&"object"===typeof c&&(c=c.Df);return H(Kf(d,a,c),S)};X.prototype.GetWords=function(a){var c=this.Df;a&&"object"===typeof a&&(a=a.Df);return H(Lf(c,a),S)};X.prototype.GetConnectedComponents=function(a){var c=this.Df;a&&"object"===typeof a&&(a=a.Df);return H(Mf(c,a),S)};
X.prototype.GetComponentImages=function(a,c,d,e,g,h,k){var m=this.Df;a&&"object"===typeof a&&(a=a.Df);c&&"object"===typeof c&&(c=c.Df);d&&"object"===typeof d&&(d=d.Df);e&&"object"===typeof e&&(e=e.Df);g&&"object"===typeof g&&(g=g.Df);h&&"object"===typeof h&&(h=h.Df);k&&"object"===typeof k&&(k=k.Df);return void 0===g?H(Nf(m,a,c,d,e),S):void 0===h?H(_emscripten_bind_TessBaseAPI_GetComponentImages_5(m,a,c,d,e,g),S):void 0===k?H(_emscripten_bind_TessBaseAPI_GetComponentImages_6(m,a,c,d,e,g,h),S):H(Of(m,
a,c,d,e,g,h,k),S)};X.prototype.GetThresholdedImageScaleFactor=function(){return Pf(this.Df)};X.prototype.AnalyseLayout=function(a){var c=this.Df;a&&"object"===typeof a&&(a=a.Df);return void 0===a?H(Qf(c),O):H(Rf(c,a),O)};X.prototype.Recognize=function(a){var c=this.Df;a&&"object"===typeof a&&(a=a.Df);return Sf(c,a)};X.prototype.FindLines=function(){return Ff(this.Df)};X.prototype.GetGradient=function(){return Gf(this.Df)};
X.prototype.ProcessPages=function(a,c,d,e){var g=this.Df;I();a=a&&"object"===typeof a?a.Df:J(a);c=c&&"object"===typeof c?c.Df:J(c);d&&"object"===typeof d&&(d=d.Df);e&&"object"===typeof e&&(e=e.Df);return!!Tf(g,a,c,d,e)};
X.prototype.ProcessPage=function(a,c,d,e,g,h){var k=this.Df;I();a&&"object"===typeof a&&(a=a.Df);c&&"object"===typeof c&&(c=c.Df);d=d&&"object"===typeof d?d.Df:J(d);e=e&&"object"===typeof e?e.Df:J(e);g&&"object"===typeof g&&(g=g.Df);h&&"object"===typeof h&&(h=h.Df);return!!Uf(k,a,c,d,e,g,h)};X.prototype.GetIterator=function(){return H(Vf(this.Df),L)};X.prototype.GetUTF8Text=function(){var a=Wf(this.Df),c=z(a);Gh(a);return c};
X.prototype.GetHOCRText=function(a){var c=this.Df;a&&"object"===typeof a&&(a=a.Df);a=Xf(c,a);c=z(a);Gh(a);return c};X.prototype.GetTSVText=function(a){var c=this.Df;a&&"object"===typeof a&&(a=a.Df);a=Zf(c,a);c=z(a);Gh(a);return c};X.prototype.GetJSONText=function(a){var c=this.Df;a&&"object"===typeof a&&(a=a.Df);a=Yf(c,a);c=z(a);Gh(a);return c};X.prototype.GetBoxText=function(a){var c=this.Df;a&&"object"===typeof a&&(a=a.Df);a=$f(c,a);c=z(a);Gh(a);return c};
X.prototype.GetUNLVText=function(){var a=ag(this.Df),c=z(a);Gh(a);return c};X.prototype.GetOsdText=function(a){var c=this.Df;a&&"object"===typeof a&&(a=a.Df);a=bg(c,a);c=z(a);Gh(a);return c};X.prototype.MeanTextConf=function(){return cg(this.Df)};X.prototype.AllWordConfidences=function(){return H(dg(this.Df),di)};X.prototype.AdaptToWordStr=function(a,c){var d=this.Df;I();a&&"object"===typeof a&&(a=a.Df);c=c&&"object"===typeof c?c.Df:J(c);return!!_emscripten_bind_TessBaseAPI_AdaptToWordStr_2(d,a,c)};
X.prototype.Clear=function(){eg(this.Df)};X.prototype.End=function(){fg(this.Df)};X.prototype.ClearPersistentCache=function(){gg(this.Df)};X.prototype.IsValidWord=function(a){var c=this.Df;I();a=a&&"object"===typeof a?a.Df:J(a);return hg(c,a)};X.prototype.IsValidCharacter=function(a){var c=this.Df;I();a=a&&"object"===typeof a?a.Df:J(a);return!!ig(c,a)};X.prototype.DetectOS=function(a){var c=this.Df;a&&"object"===typeof a&&(a=a.Df);return!!jg(c,a)};
X.prototype.GetUnichar=function(a){var c=this.Df;a&&"object"===typeof a&&(a=a.Df);return z(kg(c,a))};X.prototype.GetDawg=function(a){var c=this.Df;a&&"object"===typeof a&&(a=a.Df);return H(lg(c,a),hi)};X.prototype.NumDawgs=function(){return mg(this.Df)};X.prototype.oem=function(){return ng(this.Df)};X.prototype.__destroy__=function(){og(this.Df)};function Y(){this.Df=pg();Kh(Y)[this.Df]=this}Y.prototype=Object.create(G.prototype);Y.prototype.constructor=Y;Y.prototype.If=Y;Y.Jf={};b.OSResults=Y;
Y.prototype.print_scores=function(){qg(this.Df)};Y.prototype.get_best_result=Y.prototype.Mh=function(){return H(rg(this.Df),R)};Object.defineProperty(Y.prototype,"best_result",{get:Y.prototype.Mh});Y.prototype.get_unicharset=Y.prototype.bi=function(){return H(sg(this.Df),ci)};Object.defineProperty(Y.prototype,"unicharset",{get:Y.prototype.bi});Y.prototype.__destroy__=function(){tg(this.Df)};function Z(){throw"cannot construct a Pixa, no constructor in IDL";}Z.prototype=Object.create(G.prototype);
Z.prototype.constructor=Z;Z.prototype.If=Z;Z.Jf={};b.Pixa=Z;Z.prototype.get_n=Z.prototype.bg=function(){return ug(this.Df)};Object.defineProperty(Z.prototype,"n",{get:Z.prototype.bg});Z.prototype.get_nalloc=Z.prototype.cg=function(){return vg(this.Df)};Object.defineProperty(Z.prototype,"nalloc",{get:Z.prototype.cg});Z.prototype.get_refcount=Z.prototype.Yf=function(){return wg(this.Df)};Object.defineProperty(Z.prototype,"refcount",{get:Z.prototype.Yf});
Z.prototype.get_pix=Z.prototype.Wh=function(){return H(xg(this.Df),bi)};Object.defineProperty(Z.prototype,"pix",{get:Z.prototype.Wh});Z.prototype.get_boxa=Z.prototype.Oh=function(){return H(yg(this.Df),S)};Object.defineProperty(Z.prototype,"boxa",{get:Z.prototype.Oh});Z.prototype.__destroy__=function(){zg(this.Df)};
(function(){function a(){b.RIL_BLOCK=Ag();b.RIL_PARA=Bg();b.RIL_TEXTLINE=Cg();b.RIL_WORD=Dg();b.RIL_SYMBOL=Eg();b.OEM_TESSERACT_ONLY=Fg();b.OEM_LSTM_ONLY=Gg();b.OEM_TESSERACT_LSTM_COMBINED=Hg();b.OEM_DEFAULT=Ig();b.OEM_COUNT=Jg();b.WRITING_DIRECTION_LEFT_TO_RIGHT=Kg();b.WRITING_DIRECTION_RIGHT_TO_LEFT=Lg();b.WRITING_DIRECTION_TOP_TO_BOTTOM=Mg();b.PT_UNKNOWN=Ng();b.PT_FLOWING_TEXT=Og();b.PT_HEADING_TEXT=Pg();b.PT_PULLOUT_TEXT=Qg();b.PT_EQUATION=Rg();b.PT_INLINE_EQUATION=Sg();b.PT_TABLE=Tg();b.PT_VERTICAL_TEXT=
Ug();b.PT_CAPTION_TEXT=Vg();b.PT_FLOWING_IMAGE=Wg();b.PT_HEADING_IMAGE=Xg();b.PT_PULLOUT_IMAGE=Yg();b.PT_HORZ_LINE=Zg();b.PT_VERT_LINE=$g();b.PT_NOISE=ah();b.PT_COUNT=bh();b.DIR_NEUTRAL=ch();b.DIR_LEFT_TO_RIGHT=dh();b.DIR_RIGHT_TO_LEFT=eh();b.DIR_MIX=fh();b.JUSTIFICATION_UNKNOWN=gh();b.JUSTIFICATION_LEFT=hh();b.JUSTIFICATION_CENTER=ih();b.JUSTIFICATION_RIGHT=jh();b.TEXTLINE_ORDER_LEFT_TO_RIGHT=kh();b.TEXTLINE_ORDER_RIGHT_TO_LEFT=lh();b.TEXTLINE_ORDER_TOP_TO_BOTTOM=mh();b.ORIENTATION_PAGE_UP=nh();
b.ORIENTATION_PAGE_RIGHT=oh();b.ORIENTATION_PAGE_DOWN=ph();b.ORIENTATION_PAGE_LEFT=qh();b.PSM_OSD_ONLY=rh();b.PSM_AUTO_OSD=sh();b.PSM_AUTO_ONLY=th();b.PSM_AUTO=uh();b.PSM_SINGLE_COLUMN=vh();b.PSM_SINGLE_BLOCK_VERT_TEXT=wh();b.PSM_SINGLE_BLOCK=xh();b.PSM_SINGLE_LINE=yh();b.PSM_SINGLE_WORD=zh();b.PSM_CIRCLE_WORD=Ah();b.PSM_SINGLE_CHAR=Bh();b.PSM_SPARSE_TEXT=Ch();b.PSM_SPARSE_TEXT_OSD=Dh();b.PSM_RAW_LINE=Eh();b.PSM_COUNT=Fh()}Ca?a():Aa.unshift(a)})();
Rh.prototype.getValue=function(a){return!!Xa(this.Df+(a||0),"i8")};di.prototype.getValue=function(a){return Xa(this.Df+4*(a||0),"i32")};$h.prototype.getValue=function(a){return Xa(this.Df+4*(a||0),"float")};gi.prototype.getValue=function(a){return Xa(this.Df+8*(a||0),"double")};fi.prototype.get=Zh.prototype.get=bi.prototype.get=function(a){return Xa(this.Df+4*(a||0),"*")};function ii(){this.jg={}}ii.prototype.wrap=function(a,c){var d=Fb(4);Ya(d,0,"i32");return this.jg[a]=H(d,c)};
ii.prototype.bool=function(a){return this.wrap(a,Rh)};ii.prototype.i32=function(a){return this.wrap(a,di)};ii.prototype.f32=function(a){return this.wrap(a,$h)};ii.prototype.f64=function(a){return this.jg[a]=H(Fb(8),gi)};ii.prototype.peek=function(){var a={},c;for(c in this.jg)a[c]=this.jg[c].getValue();return a};ii.prototype.get=function(){var a={},c;for(c in this.jg)a[c]=this.jg[c].getValue(),Gh(this.jg[c].Df);return a};
L.prototype.getBoundingBox=function(a){var c=new ii;this.BoundingBox(a,c.i32("x0"),c.i32("y0"),c.i32("x1"),c.i32("y1"));return c.get()};L.prototype.getBaseline=function(a){var c=new ii;a=!!this.Baseline(a,c.i32("x0"),c.i32("y0"),c.i32("x1"),c.i32("y1"));c=c.get();c.has_baseline=a;return c};L.prototype.getRowAttributes=function(){var a=new ii;this.RowAttributes(a.f32("row_height"),a.f32("descenders"),a.f32("ascenders"));return a.get()};
L.prototype.getWordFontAttributes=function(){var a=new ii,c=this.WordFontAttributes(a.bool("is_bold"),a.bool("is_italic"),a.bool("is_underlined"),a.bool("is_monospace"),a.bool("is_serif"),a.bool("is_smallcaps"),a.i32("pointsize"),a.i32("font_id"));a=a.get();a.font_name=c;return a};b.pointerHelper=ii;


  return TesseractCore.ready
}

);
})();
if (typeof exports === 'object' && typeof module === 'object')
  module.exports = TesseractCore;
else if (typeof define === 'function' && define['amd'])
  define([], function() { return TesseractCore; });
else if (typeof exports === 'object')
  exports["TesseractCore"] = TesseractCore;
