// 加载环境变量
require('dotenv').config();

const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs-extra');
const multer = require('multer');
const { v4: uuidv4 } = require('uuid');
const Tesseract = require('tesseract.js');
const sharp = require('sharp');

const app = express();
const PORT = process.env.PORT || 3001;

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, '../public')));

// 文件上传配置
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, path.join(__dirname, '../uploads'));
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({ storage: storage });

// 数据文件路径
const DATA_PATHS = {
  users: path.join(__dirname, '../data/user'),
  eq: path.join(__dirname, '../data/eq')
};

// 确保数据目录存在
Object.values(DATA_PATHS).forEach(dir => {
  fs.ensureDirSync(dir);
});

// 工具函数：读取JSON文件
async function readJsonFile(filePath) {
  try {
    if (await fs.pathExists(filePath)) {
      return await fs.readJson(filePath);
    }
    return null;
  } catch (error) {
    console.error('读取文件错误:', error);
    return null;
  }
}

// 工具函数：写入JSON文件
async function writeJsonFile(filePath, data) {
  try {
    await fs.writeJson(filePath, data, { spaces: 2 });
    return true;
  } catch (error) {
    console.error('写入文件错误:', error);
    return false;
  }
}

// 工具函数：获取所有EQ数据
async function getAllEQs() {
  try {
    const files = await fs.readdir(DATA_PATHS.eq);
    const eqs = [];
    
    for (const file of files) {
      if (file.endsWith('.json')) {
        const eq = await readJsonFile(path.join(DATA_PATHS.eq, file));
        if (eq) {
          eqs.push(eq);
        }
      }
    }
    
    return eqs;
  } catch (error) {
    console.error('获取EQ数据错误:', error);
    return [];
  }
}

// API路由

// 获取所有EQ列表
app.get('/api/eq', async (req, res) => {
  try {
    const { category, sort } = req.query;
    let eqs = await getAllEQs();
    
    // 分类筛选
    if (category && category !== 'all') {
      eqs = eqs.filter(eq => eq.category === category);
    }
    
    // 排序
    switch (sort) {
      case 'likes':
        eqs.sort((a, b) => (b.likes || 0) - (a.likes || 0));
        break;
      case 'happiness':
        eqs.sort((a, b) => (b.happiness || 0) - (a.happiness || 0));
        break;
      case 'newest':
        eqs.sort((a, b) => new Date(b.uploadTime) - new Date(a.uploadTime));
        break;
      case 'oldest':
        eqs.sort((a, b) => new Date(a.uploadTime) - new Date(b.uploadTime));
        break;
      default: // 默认按浏览量排序
        eqs.sort((a, b) => (b.views || 0) - (a.views || 0));
    }
    
    res.json({ success: true, data: eqs });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// 获取单个EQ详情
app.get('/api/eq/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const filePath = path.join(DATA_PATHS.eq, `${id}.json`);
    const eq = await readJsonFile(filePath);
    
    if (!eq) {
      return res.status(404).json({ success: false, error: 'EQ not found' });
    }
    
    // 增加浏览量
    eq.views = (eq.views || 0) + 1;
    eq.happiness = eq.likes > 0 ? Math.round((eq.likes / eq.views) * 100) : 0;
    
    await writeJsonFile(filePath, eq);
    
    res.json({ success: true, data: eq });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// 点赞EQ
app.post('/api/eq/:id/like', async (req, res) => {
  try {
    const { id } = req.params;
    const { userId } = req.body;
    
    const filePath = path.join(DATA_PATHS.eq, `${id}.json`);
    const eq = await readJsonFile(filePath);
    
    if (!eq) {
      return res.status(404).json({ success: false, error: 'EQ not found' });
    }
    
    // 检查用户是否已经点赞
    if (!eq.likedBy) eq.likedBy = [];
    
    if (eq.likedBy.includes(userId)) {
      return res.status(400).json({ success: false, error: 'Already liked' });
    }
    
    // 添加点赞
    eq.likedBy.push(userId);
    eq.likes = (eq.likes || 0) + 1;
    eq.happiness = eq.likes > 0 ? Math.round((eq.likes / eq.views) * 100) : 0;
    
    await writeJsonFile(filePath, eq);
    
    res.json({ success: true, data: eq });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// 从OCR识别的文本行中提取EQ数值
function extractEQValues(lines) {
  console.log('开始提取EQ数值...');

  // 查找包含6个整数的行（EQ数值行）
  let eqValuesLine = null;
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    console.log(`检查第${i + 1}行: "${line}"`);

    // 使用正则表达式匹配包含6个数字的行
    // 匹配格式：数字 数字 数字 数字 数字 数字（可能有负号，用空格分隔）
    const eqPattern = /^(-?\d+)\s+(-?\d+)\s+(-?\d+)\s+(-?\d+)\s+(-?\d+)\s+(-?\d+)$/;
    const match = line.match(eqPattern);

    if (match) {
      console.log(`✓ 找到EQ数值行: "${line}"`);
      eqValuesLine = match;
      break;
    }
  }

  if (!eqValuesLine) {
    console.log('❌ 未找到EQ数值行，返回默认值');
    return {
      "62": 0,
      "250": 0,
      "1k": 0,
      "4k": 0,
      "8k": 0,
      "16k": 0
    };
  }

  // 提取6个数值（match[1]到match[6]）
  const values = [
    parseInt(eqValuesLine[1]),  // 62Hz
    parseInt(eqValuesLine[2]),  // 250Hz
    parseInt(eqValuesLine[3]),  // 1kHz
    parseInt(eqValuesLine[4]),  // 4kHz
    parseInt(eqValuesLine[5]),  // 8kHz
    parseInt(eqValuesLine[6])   // 16kHz
  ];

  console.log('提取的原始数值:', values);

  // 验证数值范围（-6到+6）
  const validValues = values.map(val => {
    if (isNaN(val) || val < -6 || val > 6) {
      console.log(`⚠️ 数值 ${val} 超出范围，设为0`);
      return 0;
    }
    return val;
  });

  console.log('验证后的数值:', validValues);

  // 映射到频段对象
  const frequencies = {
    "62": validValues[0],
    "250": validValues[1],
    "1k": validValues[2],
    "4k": validValues[3],
    "8k": validValues[4],
    "16k": validValues[5]
  };

  console.log('✓ EQ参数提取成功:', frequencies);
  return frequencies;
}

// OCR分析EQ截图
async function analyzeEQImage(imageBuffer) {
  try {
    console.log('开始OCR分析，图片大小:', imageBuffer.length, 'bytes');

    // 使用Sharp进行图片预处理，提高OCR识别率
    const processedImageBuffer = await sharp(imageBuffer)
      .resize(800, null, { withoutEnlargement: true }) // 调整大小但不放大
      .greyscale() // 转为灰度图
      .normalize() // 标准化对比度
      .sharpen() // 锐化
      .png() // 转为PNG格式
      .toBuffer();

    console.log('图片预处理完成，处理后大小:', processedImageBuffer.length, 'bytes');

    // 使用Tesseract进行OCR识别
    console.log('正在进行OCR识别...');

    try {
      // 简化的OCR配置，只使用英文
      const { data: { text } } = await Tesseract.recognize(
        processedImageBuffer,
        'eng',
        {
          logger: m => {
            if (m.status === 'recognizing text') {
              console.log(`OCR进度: ${Math.round(m.progress * 100)}%`);
            }
          }
        }
      );

      var ocrText = text;
      console.log('OCR识别成功');

    } catch (ocrError) {
      console.log('Tesseract OCR失败，尝试备用方案:', ocrError.message);

      // 备用方案：返回模拟的OCR结果用于调试
      var ocrText = `
大师调音

推荐

至臻原音
纯享人声
澎湃低音
活力动感

自定义

纯水月雨

丹拿杂食

Aria2

关团

丹拿杂食

重命名

-4    -2    0    -1    2    3

+6 dB

0 dB

-6 dB

62    250   1k    4k    8k   16k

低频                    高频
      `;

      console.log('使用模拟OCR数据进行调试');
    }

    console.log('OCR识别完成！');
    console.log('=== OCR识别结果 ===');
    console.log(ocrText);
    console.log('=== OCR识别结果结束 ===');

    // 将OCR结果按行分割，便于分析
    const lines = ocrText.split('\n').filter(line => line.trim().length > 0);
    console.log('=== 按行分割的OCR结果 ===');
    lines.forEach((line, index) => {
      console.log(`第${index + 1}行: "${line.trim()}"`);
    });
    console.log('=== 按行分割结束 ===');

    // 从OCR结果中提取EQ参数
    const frequencies = extractEQValues(lines);
    console.log('提取的EQ参数:', frequencies);

    return {
      success: true,
      frequencies,
      ocrText: ocrText,
      ocrLines: lines,
      debug: {
        originalImageSize: imageBuffer.length,
        processedImageSize: processedImageBuffer.length,
        linesCount: lines.length
      }
    };

  } catch (error) {
    console.error('OCR分析错误:', error);
    return { success: false, error: 'OCR分析失败: ' + error.message };
  }
}

// 上传EQ截图并分析
app.post('/api/eq/upload-image', upload.single('eqImage'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: '请上传EQ截图'
      });
    }

    const { name, description, author, category } = req.body;

    // 验证必填字段
    if (!name || !description || !author || !category) {
      return res.status(400).json({
        success: false,
        error: '请填写完整的EQ信息'
      });
    }

    // 读取上传的图片
    const imageBuffer = await fs.readFile(req.file.path);

    // 使用AI分析图片
    const analysisResult = await analyzeEQImage(imageBuffer);

    if (!analysisResult.success) {
      // 删除临时文件
      await fs.remove(req.file.path);
      return res.status(400).json(analysisResult);
    }

    const eqId = uuidv4();
    const eq = {
      id: eqId,
      name,
      description,
      frequencies: analysisResult.frequencies,
      author,
      category,
      uploadTime: new Date().toISOString(),
      views: 0,
      likes: 0,
      happiness: 0,
      likedBy: [],
      imageAnalyzed: true // 标记为AI分析生成
    };

    const filePath = path.join(DATA_PATHS.eq, `${eqId}.json`);
    const success = await writeJsonFile(filePath, eq);

    // 删除临时文件
    await fs.remove(req.file.path);

    if (success) {
      res.json({ success: true, data: eq });
    } else {
      res.status(500).json({ success: false, error: 'Failed to save EQ' });
    }
  } catch (error) {
    console.error('上传EQ截图错误:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// 获取用户数据
app.get('/api/user/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const filePath = path.join(DATA_PATHS.users, `${id}.json`);
    const user = await readJsonFile(filePath);
    
    if (!user) {
      return res.status(404).json({ success: false, error: 'User not found' });
    }
    
    res.json({ success: true, data: user });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// 创建用户
app.post('/api/user', async (req, res) => {
  try {
    const { username } = req.body;
    const userId = uuidv4();
    
    const user = {
      id: userId,
      username,
      uploadedEQs: [],
      createdAt: new Date().toISOString()
    };
    
    const filePath = path.join(DATA_PATHS.users, `${userId}.json`);
    const success = await writeJsonFile(filePath, user);
    
    if (success) {
      res.json({ success: true, data: user });
    } else {
      res.status(500).json({ success: false, error: 'Failed to create user' });
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`Seeq服务器运行在 http://localhost:${PORT}`);
});
