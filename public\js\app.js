// Seeq 前端应用主文件
class SeeqApp {
    constructor() {
        this.currentUser = this.getCurrentUser();
        this.eqs = [];
        this.currentCategory = localStorage.getItem('seeq_category') || 'all';
        this.currentSort = localStorage.getItem('seeq_sort') || 'views';
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.loadEQs();
        this.setupFilters();
        this.animatePageLoad();
    }
    
    // 获取当前用户（临时实现）
    getCurrentUser() {
        let userId = localStorage.getItem('seeq_user_id');
        if (!userId) {
            userId = 'temp-user-' + Date.now();
            localStorage.setItem('seeq_user_id', userId);
        }
        return userId;
    }
    
    // 设置事件监听器
    setupEventListeners() {
        // 筛选和排序
        document.getElementById('categoryFilter').addEventListener('change', (e) => {
            this.currentCategory = e.target.value;
            localStorage.setItem('seeq_category', this.currentCategory);
            this.loadEQs();
        });
        
        document.getElementById('sortFilter').addEventListener('change', (e) => {
            this.currentSort = e.target.value;
            localStorage.setItem('seeq_sort', this.currentSort);
            this.loadEQs();
        });
        
        // 上传按钮
        document.getElementById('uploadBtn').addEventListener('click', () => {
            this.showUploadModal();
        });
        
        // 模态框关闭
        document.getElementById('closeUploadModal').addEventListener('click', () => {
            this.hideUploadModal();
        });
        
        // 上传表单
        document.getElementById('uploadForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.uploadEQImage();
        });

        // 图片上传处理
        this.setupImageUpload();
        
        // 点击模态框外部关闭
        document.getElementById('eqModal').addEventListener('click', (e) => {
            if (e.target.id === 'eqModal') {
                this.hideEQModal();
            }
        });
        
        document.getElementById('uploadModal').addEventListener('click', (e) => {
            if (e.target.id === 'uploadModal') {
                this.hideUploadModal();
            }
        });
    }
    
    // 设置筛选器初始值
    setupFilters() {
        document.getElementById('categoryFilter').value = this.currentCategory;
        document.getElementById('sortFilter').value = this.currentSort;
    }

    // 设置图片上传功能
    setupImageUpload() {
        const imageUploadArea = document.getElementById('imageUploadArea');
        const imageInput = document.getElementById('eqImage');
        const uploadPlaceholder = document.getElementById('uploadPlaceholder');
        const imagePreview = document.getElementById('imagePreview');
        const previewImg = document.getElementById('previewImg');
        const submitBtn = document.getElementById('submitBtn');
        const submitText = document.getElementById('submitText');

        // 点击上传区域
        imageUploadArea.addEventListener('click', () => {
            imageInput.click();
        });

        // 拖拽上传
        imageUploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            imageUploadArea.classList.add('border-blue-400', 'bg-blue-50');
        });

        imageUploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            imageUploadArea.classList.remove('border-blue-400', 'bg-blue-50');
        });

        imageUploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            imageUploadArea.classList.remove('border-blue-400', 'bg-blue-50');

            const files = e.dataTransfer.files;
            if (files.length > 0 && files[0].type.startsWith('image/')) {
                imageInput.files = files;
                this.handleImageSelect(files[0]);
            }
        });

        // 文件选择
        imageInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                this.handleImageSelect(e.target.files[0]);
            }
        });
    }

    // 处理图片选择
    handleImageSelect(file) {
        const uploadPlaceholder = document.getElementById('uploadPlaceholder');
        const imagePreview = document.getElementById('imagePreview');
        const previewImg = document.getElementById('previewImg');
        const submitBtn = document.getElementById('submitBtn');
        const submitText = document.getElementById('submitText');

        // 显示图片预览
        const reader = new FileReader();
        reader.onload = (e) => {
            previewImg.src = e.target.result;
            uploadPlaceholder.classList.add('hidden');
            imagePreview.classList.remove('hidden');

            // 启用提交按钮
            submitBtn.disabled = false;
            submitText.textContent = '上传并分析EQ设置';
        };
        reader.readAsDataURL(file);
    }
    

    
    // 加载EQ数据
    async loadEQs() {
        try {
            this.showLoading();
            
            const response = await fetch(`/api/eq?category=${this.currentCategory}&sort=${this.currentSort}`);
            const result = await response.json();
            
            if (result.success) {
                this.eqs = result.data;
                this.renderEQs();
            } else {
                console.error('加载EQ失败:', result.error);
                this.showEmptyState();
            }
        } catch (error) {
            console.error('网络错误:', error);
            this.showEmptyState();
        }
    }
    
    // 渲染EQ卡片
    renderEQs() {
        const grid = document.getElementById('eqGrid');
        const loading = document.getElementById('loading');
        const emptyState = document.getElementById('emptyState');
        
        loading.classList.add('hidden');
        
        if (this.eqs.length === 0) {
            grid.classList.add('hidden');
            emptyState.classList.remove('hidden');
            return;
        }
        
        emptyState.classList.add('hidden');
        grid.classList.remove('hidden');
        
        grid.innerHTML = '';
        
        this.eqs.forEach((eq, index) => {
            const card = this.createEQCard(eq);
            grid.appendChild(card);
            
            // 添加进入动画
            gsap.fromTo(card, 
                { opacity: 0, y: 50 },
                { opacity: 1, y: 0, duration: 0.6, delay: index * 0.1 }
            );
        });
    }
    
    // 创建EQ卡片
    createEQCard(eq) {
        const card = document.createElement('div');
        card.className = 'bg-white rounded-2xl p-6 shadow-lg card-hover cursor-pointer';
        
        card.innerHTML = `
            <div class="flex items-start justify-between mb-4">
                <div class="flex-1">
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">${eq.name}</h3>
                    <p class="text-gray-600 text-sm line-clamp-2">${eq.description}</p>
                </div>
                <div class="ml-4 text-right">
                    <div class="text-xs text-gray-500 mb-1">${eq.category}</div>
                    <div class="text-xs text-gray-500">by ${eq.author}</div>
                </div>
            </div>

            <div class="flex items-center justify-between text-sm text-gray-500">
                <span>👁️ ${eq.views || 0}</span>
                <span>❤️ ${eq.likes || 0}</span>
                <span>😊 ${eq.happiness || 0}%</span>
            </div>
        `;
        
        card.addEventListener('click', () => {
            this.showEQDetails(eq.id);
        });
        
        return card;
    }
    

    
    // 显示EQ详情
    async showEQDetails(eqId) {
        try {
            const response = await fetch(`/api/eq/${eqId}`);
            const result = await response.json();
            
            if (result.success) {
                const eq = result.data;
                this.renderEQModal(eq);
                this.showEQModal();
            }
        } catch (error) {
            console.error('获取EQ详情失败:', error);
        }
    }
    
    // 渲染EQ模态框
    renderEQModal(eq) {
        const modalContent = document.getElementById('modalContent');
        const canLike = !eq.likedBy || !eq.likedBy.includes(this.currentUser);
        
        modalContent.innerHTML = `
            <div class="p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-xl font-bold text-gray-800">${eq.name}</h3>
                    <button onclick="seeqApp.hideEQModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                
                <div class="mb-6">
                    <p class="text-gray-600 mb-4">${eq.description}</p>
                    <div class="flex items-center justify-between text-sm text-gray-500">
                        <span>作者: ${eq.author}</span>
                        <span>${eq.category}</span>
                    </div>
                </div>
                
                <div class="mb-6">
                    <h4 class="font-semibold text-gray-800 mb-4">EQ设置</h4>
                    <div class="bg-gray-50 rounded-xl p-4 mb-4">
                        ${this.renderEQChart(eq.frequencies)}
                    </div>
                    <div class="flex justify-between items-end px-4">
                        ${this.renderEQSliders(eq.frequencies)}
                    </div>
                </div>
                
                <div class="flex items-center justify-between mb-6">
                    <div class="flex items-center space-x-4 text-sm text-gray-500">
                        <span>👁️ ${eq.views || 0} 浏览</span>
                        <span>❤️ ${eq.likes || 0} 点赞</span>
                        <span>😊 ${eq.happiness || 0}% 欢率</span>
                    </div>
                </div>
                
                <div class="flex space-x-3">
                    <button onclick="seeqApp.importToHuanlu('${eq.id}')"
                            class="flex-1 gradient-bg text-white py-3 rounded-xl font-medium hover:opacity-90 transition-opacity">
                        🎵 一键导入欢律
                    </button>
                    <button ${canLike ? '' : 'disabled'}
                            onclick="seeqApp.likeEQ('${eq.id}')"
                            class="px-6 py-3 ${canLike ? 'bg-white border border-gray-300 hover:bg-gray-50' : 'bg-gray-100 border border-gray-200'} rounded-xl font-medium transition-colors ${canLike ? '' : 'cursor-not-allowed'}">
                        ${canLike ? '❤️ 点赞' : '✅ 已点赞'}
                    </button>
                </div>
            </div>
        `;
    }
    
    // 渲染EQ图表（详情页面）
    renderEQChart(frequencies) {
        const freqs = ['62', '250', '1k', '4k', '8k', '16k'];
        const values = freqs.map(freq => frequencies[freq] || 0);

        // 创建SVG图表
        let svg = `
            <div class="relative h-32">
                <svg width="100%" height="100%" viewBox="0 0 300 120" class="rounded-lg">
                    <!-- 网格线 -->
                    <defs>
                        <pattern id="grid" width="50" height="20" patternUnits="userSpaceOnUse">
                            <path d="M 50 0 L 0 0 0 20" fill="none" stroke="#e5e7eb" stroke-width="0.5"/>
                        </pattern>
                    </defs>
                    <rect width="100%" height="100%" fill="url(#grid)" />

                    <!-- 中心线 -->
                    <line x1="0" y1="60" x2="300" y2="60" stroke="#9ca3af" stroke-width="1.5" opacity="0.8"/>

                    <!-- EQ曲线 -->
                    <path d="${this.generateEQPath(values)}" fill="rgba(59, 130, 246, 0.2)" stroke="#3b82f6" stroke-width="2"/>

                    <!-- 控制点 -->
                    ${values.map((value, index) => {
                        const x = index * 50 + 25;
                        const y = 60 - (value * 8); // 每dB对应8像素
                        return `<circle cx="${x}" cy="${y}" r="4" fill="#3b82f6" stroke="#ffffff" stroke-width="2"/>`;
                    }).join('')}
                </svg>

                <!-- 频段标签 -->
                <div class="absolute bottom-1 left-0 right-0 flex justify-between px-4 text-xs text-gray-500">
                    ${freqs.map(freq => `<span>${freq}</span>`).join('')}
                </div>

                <!-- dB标签 -->
                <div class="absolute left-1 top-0 bottom-0 flex flex-col justify-between py-1 text-xs text-gray-500">
                    <span>+6</span>
                    <span>0</span>
                    <span>-6</span>
                </div>
            </div>
        `;

        return svg;
    }

    // 生成EQ曲线路径
    generateEQPath(values) {
        let path = `M 0 ${60 - (values[0] * 8)}`;

        values.forEach((value, index) => {
            const x = index * 50 + 25;
            const y = 60 - (value * 8);

            if (index === 0) {
                path += ` L ${x} ${y}`;
            } else {
                // 平滑曲线
                const prevX = (index - 1) * 50 + 25;
                const prevY = 60 - (values[index - 1] * 8);
                const cpX1 = prevX + 15;
                const cpX2 = x - 15;
                path += ` C ${cpX1} ${prevY} ${cpX2} ${y} ${x} ${y}`;
            }
        });

        path += ` L 300 ${60 - (values[values.length - 1] * 8)}`;
        path += ` L 300 120 L 0 120 Z`;

        return path;
    }

    // 渲染EQ滑块显示（详情页面）
    renderEQSliders(frequencies) {
        const freqs = [
            { key: '62', label: '62' },
            { key: '250', label: '250' },
            { key: '1k', label: '1k' },
            { key: '4k', label: '4k' },
            { key: '8k', label: '8k' },
            { key: '16k', label: '16k' }
        ];

        return freqs.map(freq => {
            const value = frequencies[freq.key] || 0;
            const height = Math.abs(value) * 8 + 20; // 最小20px高度
            const color = value > 0 ? '#10b981' : value < 0 ? '#ef4444' : '#9ca3af';

            return `
                <div class="flex flex-col items-center space-y-1">
                    <div class="text-xs text-blue-600 font-semibold">${value > 0 ? '+' : ''}${value}</div>
                    <div class="w-6 rounded-full" style="height: ${height}px; background-color: ${color}; opacity: 0.8;"></div>
                    <div class="text-xs text-gray-500">${freq.label}</div>
                </div>
            `;
        }).join('');
    }
    
    // 点赞EQ
    async likeEQ(eqId) {
        try {
            const response = await fetch(`/api/eq/${eqId}/like`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ userId: this.currentUser })
            });
            
            const result = await response.json();
            
            if (result.success) {
                // 重新显示详情以更新数据
                this.showEQDetails(eqId);
                // 重新加载列表
                this.loadEQs();
            } else {
                alert(result.error === 'Already liked' ? '您已经点赞过了' : '点赞失败');
            }
        } catch (error) {
            console.error('点赞失败:', error);
            alert('点赞失败，请稍后重试');
        }
    }
    
    // 一键导入欢律
    importToHuanlu(eqId) {
        const eq = this.eqs.find(e => e.id === eqId);
        if (eq) {
            // 按照频段顺序获取数值：62, 250, 1k, 4k, 8k, 16k
            const frequencies = ['62', '250', '1k', '4k', '8k', '16k'];
            const values = frequencies.map(freq => {
                const value = eq.frequencies[freq] || 0;
                return value > 0 ? `+${value}` : `${value}`;
            });

            // 输出到控制台供外部程序检测
            const importCommand = `[Seeq]{${values.join(',')}}`;
            console.log(importCommand);

            // 显示成功提示
            alert(`EQ参数已输出到控制台！\n\n${eq.name}\n参数: ${values.join(', ')}\n\n外部程序将自动检测并导入到欢律APP。`);
        }
    }
    
    // 上传EQ图片并分析
    async uploadEQImage() {
        const form = document.getElementById('uploadForm');
        const formData = new FormData(form);
        const submitBtn = document.getElementById('submitBtn');
        const submitText = document.getElementById('submitText');
        const loadingSpinner = document.getElementById('loadingSpinner');

        // 显示加载状态
        submitBtn.disabled = true;
        submitText.classList.add('hidden');
        loadingSpinner.classList.remove('hidden');

        try {
            const response = await fetch('/api/eq/upload-image', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                // 显示OCR分析结果
                console.log('收到的分析结果:', result.data);
                this.showAnalysisResult(result.data);

                // 延迟显示成功消息
                setTimeout(() => {
                    alert('EQ设置上传成功！OCR已成功分析您的截图。\n\n请查看调试信息，然后告诉开发者如何提取EQ参数。');
                    this.hideUploadModal();
                    this.loadEQs(); // 重新加载列表
                    this.resetUploadForm();
                }, 2000); // 增加延迟时间，让用户有时间查看调试信息
            } else {
                alert('上传失败: ' + result.error);
                this.resetSubmitButton();
            }
        } catch (error) {
            console.error('上传失败:', error);
            alert('上传失败，请稍后重试');
            this.resetSubmitButton();
        }
    }

    // 显示OCR分析结果
    showAnalysisResult(data) {
        const analysisResult = document.getElementById('aiAnalysisResult');
        const analysisPreview = document.getElementById('analysisPreview');

        const frequencies = data.frequencies || {};
        const ocrText = data.ocrText || '';
        const ocrLines = data.ocrLines || [];
        const debug = data.debug || {};

        const freqs = [
            { key: '62', label: '62Hz' },
            { key: '250', label: '250Hz' },
            { key: '1k', label: '1kHz' },
            { key: '4k', label: '4kHz' },
            { key: '8k', label: '8kHz' },
            { key: '16k', label: '16kHz' }
        ];

        const resultHTML = `
            <div class="text-sm text-green-600 mb-3">✓ OCR分析完成！</div>

            <!-- EQ参数结果 -->
            <div class="mb-4">
                <div class="text-sm font-medium text-gray-700 mb-2">提取的EQ参数：</div>
                <div class="grid grid-cols-3 gap-2 text-sm">
                    ${freqs.map(freq => {
                        const value = frequencies[freq.key] || 0;
                        return `
                            <div class="flex justify-between items-center bg-white rounded-lg px-3 py-2">
                                <span class="text-gray-600">${freq.label}</span>
                                <span class="font-semibold ${value > 0 ? 'text-green-600' : value < 0 ? 'text-red-600' : 'text-gray-600'}">
                                    ${value > 0 ? '+' : ''}${value}dB
                                </span>
                            </div>
                        `;
                    }).join('')}
                </div>
            </div>

            <!-- OCR调试信息 -->
            <div class="border-t pt-4">
                <div class="text-sm font-medium text-gray-700 mb-2">OCR调试信息：</div>
                <div class="text-xs text-gray-600 mb-2">
                    原图大小: ${debug.originalImageSize ? Math.round(debug.originalImageSize / 1024) + 'KB' : '未知'} |
                    处理后: ${debug.processedImageSize ? Math.round(debug.processedImageSize / 1024) + 'KB' : '未知'} |
                    识别行数: ${debug.linesCount || ocrLines.length}
                </div>

                <!-- 完整OCR文本 -->
                <details class="mb-3">
                    <summary class="text-sm font-medium text-gray-700 cursor-pointer hover:text-blue-600">
                        完整OCR识别文本 (点击展开)
                    </summary>
                    <div class="mt-2 p-3 bg-gray-100 rounded text-xs font-mono whitespace-pre-wrap max-h-32 overflow-y-auto">
${ocrText}
                    </div>
                </details>

                <!-- 按行分割的文本 -->
                <details>
                    <summary class="text-sm font-medium text-gray-700 cursor-pointer hover:text-blue-600">
                        按行分割的文本 (点击展开)
                    </summary>
                    <div class="mt-2 max-h-40 overflow-y-auto">
                        ${ocrLines.map((line, index) => `
                            <div class="text-xs p-2 border-b border-gray-200 font-mono">
                                <span class="text-gray-500 mr-2">${index + 1}:</span>
                                <span class="text-gray-800">"${line.trim()}"</span>
                            </div>
                        `).join('')}
                    </div>
                </details>
            </div>
        `;

        analysisPreview.innerHTML = resultHTML;
        analysisResult.classList.remove('hidden');

        // 添加动画效果
        gsap.fromTo(analysisResult,
            { opacity: 0, y: 20 },
            { opacity: 1, y: 0, duration: 0.5 }
        );
    }

    // 重置提交按钮
    resetSubmitButton() {
        const submitBtn = document.getElementById('submitBtn');
        const submitText = document.getElementById('submitText');
        const loadingSpinner = document.getElementById('loadingSpinner');

        submitBtn.disabled = false;
        submitText.classList.remove('hidden');
        loadingSpinner.classList.add('hidden');
        submitText.textContent = '上传并分析EQ设置';
    }

    // 重置上传表单
    resetUploadForm() {
        document.getElementById('uploadForm').reset();
        document.getElementById('uploadPlaceholder').classList.remove('hidden');
        document.getElementById('imagePreview').classList.add('hidden');
        document.getElementById('aiAnalysisResult').classList.add('hidden');

        const submitBtn = document.getElementById('submitBtn');
        const submitText = document.getElementById('submitText');
        submitBtn.disabled = true;
        submitText.textContent = '请先上传EQ截图';

        this.resetSubmitButton();
    }
    

    
    // 显示/隐藏模态框
    showEQModal() {
        const modal = document.getElementById('eqModal');
        modal.classList.remove('hidden');
        gsap.fromTo(modal.querySelector('.bg-white'), 
            { scale: 0.8, opacity: 0 },
            { scale: 1, opacity: 1, duration: 0.3 }
        );
    }
    
    hideEQModal() {
        const modal = document.getElementById('eqModal');
        gsap.to(modal.querySelector('.bg-white'), {
            scale: 0.8,
            opacity: 0,
            duration: 0.2,
            onComplete: () => modal.classList.add('hidden')
        });
    }
    
    showUploadModal() {
        const modal = document.getElementById('uploadModal');
        modal.classList.remove('hidden');
        gsap.fromTo(modal.querySelector('.bg-white'), 
            { scale: 0.8, opacity: 0 },
            { scale: 1, opacity: 1, duration: 0.3 }
        );
    }
    
    hideUploadModal() {
        const modal = document.getElementById('uploadModal');
        gsap.to(modal.querySelector('.bg-white'), {
            scale: 0.8,
            opacity: 0,
            duration: 0.2,
            onComplete: () => modal.classList.add('hidden')
        });
    }
    
    // 显示加载状态
    showLoading() {
        document.getElementById('loading').classList.remove('hidden');
        document.getElementById('eqGrid').classList.add('hidden');
        document.getElementById('emptyState').classList.add('hidden');
    }
    
    // 显示空状态
    showEmptyState() {
        document.getElementById('loading').classList.add('hidden');
        document.getElementById('eqGrid').classList.add('hidden');
        document.getElementById('emptyState').classList.remove('hidden');
    }
    
    // 页面加载动画
    animatePageLoad() {
        gsap.fromTo('nav', 
            { y: -100, opacity: 0 },
            { y: 0, opacity: 1, duration: 0.6 }
        );
        
        gsap.fromTo('main > div:first-child', 
            { y: 50, opacity: 0 },
            { y: 0, opacity: 1, duration: 0.8, delay: 0.2 }
        );
        
        gsap.fromTo('.floating-action', 
            { scale: 0, rotation: 180 },
            { scale: 1, rotation: 0, duration: 0.6, delay: 0.8 }
        );
    }
}

// 初始化应用
const seeqApp = new SeeqApp();
