'use strict';

/**
 *
 * Browser worker scripts
 *
 * @fileoverview Browser worker implementation
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <jero<PERSON><PERSON><EMAIL>>
 */

const worker = require('..');
const getCore = require('./getCore');
const gunzip = require('./gunzip');
const cache = require('./cache');

/*
 * register message handler
 */
global.addEventListener('message', ({ data }) => {
  worker.dispatchHandlers(data, (obj) => postMessage(obj));
});

/*
 * getCore is a sync function to load and return
 * TesseractCore.
 */
worker.setAdapter({
  getCore,
  gunzip,
  fetch: () => {},
  ...cache,
});
