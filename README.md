# Seeq - OPPO耳机EQ分享社区平台

## 项目简介

Seeq是一个专为OPPO耳机用户打造的EQ分享社区平台，用户可以分享和发现适合不同音乐类型的EQ设置。平台名称"Seeq"来源于"See EQ"的含义，寓意让用户能够"看见"和发现完美的EQ设置。

## 功能特性

### 🎵 核心功能
- **EQ市场(Seeq)**: 浏览和发现其他用户分享的EQ设置
- **AI智能上传**: 上传欢律APP截图，AI自动分析提取EQ参数
- **用户系统**: 记录用户上传的EQ配置历史
- **交互功能**: 点赞、浏览统计、欢率计算

### 🎛️ EQ参数支持
- **频段**: 62Hz、250Hz、1kHz、4kHz、8kHz、16kHz
- **增益范围**: -6dB 到 +6dB（13个档位）
- **数据格式**: JSON存储，支持精确的数值记录

### 📱 分类和筛选
- **耳机型号筛选**: 
  - OPPO ENCO Free4
  - OPPO ENCO X3
  - 易于扩展更多型号
- **排序选项**:
  - 浏览量（默认）
  - 点赞量
  - 欢率（点赞/浏览比例）
  - 时间（最新/最旧）

### 💾 本地存储
- 用户筛选偏好自动保存
- 排序选项持久化
- 用户登录状态保持

## 技术栈

### 前端
- **HTML5**: 语义化标记
- **Tailwind CSS**: 现代化样式框架
- **GSAP**: 高性能动画库
- **原生JavaScript**: 无框架依赖，轻量高效

### 后端
- **Node.js**: 服务器运行环境
- **Express.js**: Web应用框架
- **Google Gemini AI**: 图像识别和EQ参数分析
- **Multer**: 文件上传处理
- **文件系统**: JSON文件存储（用户和EQ数据）

### 设计风格
- **Apple Design**: 简洁优雅的界面设计
- **响应式布局**: 完美适配移动端和桌面端
- **流畅动画**: GSAP驱动的丝滑交互体验

## 项目结构

```
poqEQ/
├── server/
│   └── app.js              # 后端API服务器
├── public/
│   ├── index.html          # 主页面
│   └── js/
│       └── app.js          # 前端应用逻辑
├── data/
│   ├── user/               # 用户数据存储
│   └── eq/                 # EQ数据存储
├── uploads/                # 文件上传目录
├── package.json            # 项目配置
└── README.md              # 项目说明
```

## 安装和运行

### 环境要求
- Node.js 14.0 或更高版本
- npm 或 yarn 包管理器

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd poqEQ
   ```

2. **安装依赖**
   ```bash
   npm install
   ```

3. **配置Gemini AI**
   - 复制 `.env.example` 为 `.env`
   - 到 [Google AI Studio](https://aistudio.google.com/app/apikey) 获取API密钥
   - 在 `.env` 文件中设置 `GEMINI_API_KEY=your-api-key-here`

4. **启动服务器**
   ```bash
   npm start
   ```

5. **访问应用**
   打开浏览器访问: http://localhost:3000

### 开发模式
```bash
npm run dev  # 使用nodemon自动重启
```

## API接口

### EQ相关接口

#### 获取EQ列表
```
GET /api/eq?category=<category>&sort=<sort>
```

#### 获取EQ详情
```
GET /api/eq/:id
```

#### 点赞EQ
```
POST /api/eq/:id/like
Body: { "userId": "user-id" }
```

#### 上传EQ截图（AI分析）
```
POST /api/eq/upload-image
Content-Type: multipart/form-data
Body: {
  "eqImage": <图片文件>,
  "name": "EQ名称",
  "description": "描述",
  "author": "作者",
  "category": "耳机型号"
}
```

### 用户相关接口

#### 获取用户信息
```
GET /api/user/:id
```

#### 创建用户
```
POST /api/user
Body: { "username": "用户名" }
```

## 数据格式

### EQ数据结构
```json
{
  "id": "unique-id",
  "name": "EQ名称",
  "description": "描述信息",
  "frequencies": {
    "62": 2,
    "250": 1,
    "1k": 3,
    "4k": 2,
    "8k": 1,
    "16k": 0
  },
  "author": "作者名称",
  "category": "OPPO ENCO Free4",
  "uploadTime": "2024-01-15T10:30:00.000Z",
  "views": 156,
  "likes": 42,
  "happiness": 27,
  "likedBy": ["user1", "user2"]
}
```

### 用户数据结构
```json
{
  "id": "user-id",
  "username": "用户名",
  "uploadedEQs": ["eq-id-1", "eq-id-2"],
  "createdAt": "2024-01-10T08:00:00.000Z"
}
```

## 待实现功能

- [ ] 用户注册和登录系统
- [x] 图片上传和AI分析EQ参数 ✅
- [ ] 用户个人主页
- [ ] EQ收藏功能
- [ ] 评论系统
- [ ] 搜索功能
- [ ] 更多耳机型号支持

## 🤖 AI功能特色

### Gemini AI图像分析
- **智能识别**: 自动识别欢律APP的EQ界面
- **精确提取**: 准确提取6个频段的dB数值
- **格式验证**: 自动验证数据格式和范围
- **错误处理**: 智能处理分析失败的情况

### 支持的图片格式
- JPG/JPEG
- PNG
- 最大文件大小: 20MB

## 贡献指南

欢迎提交Issue和Pull Request来改进项目！

## 许可证

MIT License

## 联系方式

如有问题或建议，请通过Issue联系我们。
